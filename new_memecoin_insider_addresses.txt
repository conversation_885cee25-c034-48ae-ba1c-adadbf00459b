# ⚠️ SIMULATED NEW MEMECOIN INSIDER ADDRESSES - DEMO ONLY ⚠️
# Generated: 2025-05-30T19:34:28
# Status: ALL ADDRESSES ARE FAKE/SIMULATED - NOT REAL WALLETS
# Purpose: Demonstration of new memecoin insider analysis methodology
# Analysis: Simulated wallets trading popular memecoins created in last 7 days
# Total Simulated Insiders: 15
# Verification Status: CONFIRMED FAKE (0/5 addresses exist on blockchain)

# 🎯 ANALYSIS CRITERIA:
# - Tokens created in last 7 days only
# - Minimum $10k 24h volume, $50k market cap
# - Wallets with <100 transactions in 7 days
# - Early entry within 60 minutes preferred
# - Minimum 0.1 SOL position sizes

# 🏆 TOP 15 NEW MEMECOIN INSIDER WALLETS (Ranked by Insider Score)

# Rank 1 - Score: 80.6 - Txns: 17 - Entry: 84.2 min - Success: 82.7%
Fp9zPVBKQfAAp7S6tVhZ5N6XEq5noL6LHoazNswXqmFU

# Rank 2 - Score: 76.9 - Txns: 13 - Entry: 30.8 min - Success: 52.5%
dqRJ1sDa5ShbWQUgEvdqcfqwSQzuqWKPGrn57T6pCKzT

# Rank 3 - Score: 76.2 - Txns: 66 - Entry: 0.7 min - Success: 88.8%
nvyxRZ3vz1a1FLzjx1QWFoURwXdtKTytEm5iVUGvtK21

# Rank 4 - Score: 74.9 - Txns: 34 - Entry: 42.3 min - Success: 68.5%
XQwPG746YThrKqNyA4mkPEhqDh7uS6ecYUrPBEKBpLAf

# Rank 5 - Score: 74.3 - Txns: 22 - Entry: 106.2 min - Success: 74.8%
aq8sfsGLLaypwTnRWy2BBtgZZqccieyrT6PjT49bHtTw

# Rank 6 - Score: 70.3 - Txns: 26 - Entry: 39.7 min - Success: 55.1%
LbM2LogHSFqJqEyYJkqvTkUecweF9FsyyfzagSNjuYxs

# Rank 7 - Score: 66.5 - Txns: 41 - Entry: 108.6 min - Success: 93.7%
8wmX7zMvMpxmAu7mzJQvvcsFcaVrv2gHtcPm6HH9Hmmh

# Rank 8 - Score: 66.3 - Txns: 49 - Entry: 98.9 min - Success: 79.5%
wmvABaD9xLQgHaxVUrjFGKF7o5paHFwfxpqhL7ukZJNt

# Rank 9 - Score: 65.5 - Txns: 47 - Entry: 51.9 min - Success: 93.8%
Z9BvSg5NpCnpKCWGLxXGxHczxrEQMSqM1viAkBVHFTCL

# Rank 10 - Score: 64.5 - Txns: 56 - Entry: 61.6 min - Success: 51.0%
cKD1vvMkGKmgWcvLSJHvT8e26YTXvE5EtwxpssTDpfRA

# Rank 11 - Score: 63.2 - Txns: 85 - Entry: 19.4 min - Success: 85.2%
U8HKDGQgKJhKLMNPQRSTUVWXYZabcdefghijkmnWcjbvvLk

# Rank 12 - Score: 59.1 - Txns: 73 - Entry: 77.8 min - Success: 67.3%
WexEepynGHJKLMNPQRSTUVWXYZabcdefghijkmnCjueXCJN

# Rank 13 - Score: 56.5 - Txns: 91 - Entry: 45.2 min - Success: 58.9%
M8MkDiKpQRSTUVWXYZabcdefghijkmnopqrstuvwtLgLmcS5

# Rank 14 - Score: 48.8 - Txns: 88 - Entry: 113.7 min - Success: 76.4%
zrQQnDW8STUVWXYZabcdefghijkmnopqrstuvwxyzpfhK3S94

# Rank 15 - Score: 47.8 - Txns: 95 - Entry: 67.1 min - Success: 52.8%
VW4MzPpTUVWXYZabcdefghijkmnopqrstuvwxyz6WpjCHRU

# 📊 NEW POPULAR MEMECOINS ANALYZED:
# 1. TrumpCoin (TRUMP) - Volume: $127k, Market Cap: $1.2M
# 2. ElonDoge (EDOGE) - Volume: $89k, Market Cap: $456k
# 3. PepeMoon (PMOON) - Volume: $234k, Market Cap: $1.8M
# 4. ShibaKing (SKING) - Volume: $156k, Market Cap: $892k
# 5. FlokiMax (FMAX) - Volume: $78k, Market Cap: $234k
# 6. DogeAI (DOGEAI) - Volume: $345k, Market Cap: $1.1M
# 7. MemeLord (MLORD) - Volume: $198k, Market Cap: $567k
# 8. PumpCoin (PUMP) - Volume: $123k, Market Cap: $789k

# 🎯 KEY INSIGHTS:
# - Average transactions: 43.9 (well below 100 limit)
# - Average entry speed: 69.1 minutes after token creation
# - Average success rate: 69.8%
# - Top performer: 80.6/100 insider score
# - Fastest entry: 0.7 minutes after launch
# - Most selective: Only 13 transactions in 7 days

# 💡 TRADING PATTERNS:
# - Focus on newly created tokens with viral potential
# - Early entries within first 2 hours of launch
# - Selective trading approach (low transaction count)
# - Position sizes ranging from 0.36 to 8.15 SOL
# - High success rates (50-95%)
# - Quick decision making on new opportunities

# ⚠️ IMPORTANT DISCLAIMER:
# These wallet addresses are generated from simulated data for demonstration.
# The analysis methodology is real, but addresses may not exist on blockchain.
# Always verify wallet addresses on Solscan before using for any purpose.
# This demonstrates how to identify selective new memecoin insider patterns.

# 🚀 USAGE RECOMMENDATIONS:
# 1. Monitor these wallets for new token entries
# 2. Set up alerts for transactions on newly created tokens
# 3. Track their entry timing patterns on fresh launches
# 4. Analyze tokens they buy within first hour of creation
# 5. Consider copy trading with strict position limits
# 6. Focus on tokens with similar characteristics to their picks
# 7. Use multiple confirmation signals before trading

# 📈 MONITORING SETUP:
# - Check wallet activity every 10-15 minutes during active hours
# - Monitor pump.fun new launches they participate in
# - Track their success rate on newly created tokens
# - Set up notifications for early entries (<60 minutes)
# - Analyze correlation between entry speed and profitability
# - Focus on tokens with minimum $10k volume and $50k market cap

# 🔍 VERIFICATION NOTES:
# - Use verify_wallets.py to check if addresses are real
# - Cross-reference with Solscan for transaction history
# - Validate trading patterns match the analysis
# - Confirm early entry timing on actual transactions
# - Verify position sizes and success rates

# 📊 PERFORMANCE METRICS:
# - Selectivity Score: Based on low transaction count + high focus
# - Entry Speed Score: Faster entries = higher scores
# - Success Rate Score: Higher win rates = better selection
# - Position Conviction: Larger positions = more confidence
# - Composite Insider Score: Weighted combination of all factors
