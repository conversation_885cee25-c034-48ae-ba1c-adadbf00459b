#!/usr/bin/env python3
"""
Verify wallet addresses on Solana blockchain.
Checks if addresses exist and have transaction history.
"""

import asyncio
import aiohttp
import json
from typing import List, Dict, Any


class WalletVerifier:
    """Verifies wallet addresses on Solana blockchain."""
    
    def __init__(self):
        self.session = None
        self.solana_rpc = "https://api.mainnet-beta.solana.com"
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            headers={"Content-Type": "application/json"}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def verify_wallet(self, address: str) -> Dict[str, Any]:
        """Verify a single wallet address."""
        try:
            # Get account info
            account_info = await self._get_account_info(address)
            
            if not account_info:
                return {
                    "address": address,
                    "exists": False,
                    "balance_sol": 0,
                    "transaction_count": 0,
                    "status": "NOT_FOUND"
                }
            
            # Get balance
            balance_lamports = account_info.get('lamports', 0)
            balance_sol = balance_lamports / 1e9
            
            # Get transaction count
            tx_count = await self._get_transaction_count(address)
            
            # Determine status
            if balance_sol > 0 or tx_count > 0:
                status = "ACTIVE"
            else:
                status = "INACTIVE"
            
            return {
                "address": address,
                "exists": True,
                "balance_sol": balance_sol,
                "transaction_count": tx_count,
                "status": status,
                "solscan_url": f"https://solscan.io/account/{address}"
            }
            
        except Exception as e:
            return {
                "address": address,
                "exists": False,
                "balance_sol": 0,
                "transaction_count": 0,
                "status": f"ERROR: {str(e)}"
            }
    
    async def _get_account_info(self, address: str) -> Dict[str, Any]:
        """Get account information from Solana RPC."""
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getAccountInfo",
            "params": [
                address,
                {
                    "encoding": "base64",
                    "commitment": "confirmed"
                }
            ]
        }
        
        try:
            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get('result')
                    if result and result.get('value'):
                        return result['value']
                return None
        except Exception:
            return None
    
    async def _get_transaction_count(self, address: str) -> int:
        """Get transaction count for an address."""
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getSignaturesForAddress",
            "params": [
                address,
                {
                    "limit": 1,
                    "commitment": "confirmed"
                }
            ]
        }
        
        try:
            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get('result', [])
                    # This is just checking if there are any transactions
                    # For exact count, we'd need to paginate through all
                    return len(result) if result else 0
                return 0
        except Exception:
            return 0
    
    async def verify_multiple_wallets(self, addresses: List[str]) -> List[Dict[str, Any]]:
        """Verify multiple wallet addresses."""
        print(f"🔍 Verifying {len(addresses)} wallet addresses...")
        print("=" * 60)
        
        results = []
        
        for i, address in enumerate(addresses, 1):
            print(f"Checking {i}/{len(addresses)}: {address[:8]}...{address[-8:]}")
            
            result = await self.verify_wallet(address)
            results.append(result)
            
            # Print immediate result
            if result['exists']:
                print(f"   ✅ {result['status']} - Balance: {result['balance_sol']:.4f} SOL")
                print(f"   🔗 {result['solscan_url']}")
            else:
                print(f"   ❌ {result['status']}")
            
            # Rate limiting
            await asyncio.sleep(0.5)
        
        return results


def load_addresses_from_file(filename: str = "insider_addresses.txt") -> List[str]:
    """Load wallet addresses from file."""
    addresses = []
    
    try:
        with open(filename, 'r') as f:
            for line in f:
                line = line.strip()
                # Skip comments and empty lines
                if line and not line.startswith('#'):
                    # Basic validation - Solana addresses are 32-44 characters
                    if 32 <= len(line) <= 44:
                        addresses.append(line)
        
        print(f"📁 Loaded {len(addresses)} addresses from {filename}")
        return addresses
        
    except FileNotFoundError:
        print(f"❌ File {filename} not found")
        return []
    except Exception as e:
        print(f"❌ Error loading addresses: {e}")
        return []


async def main():
    """Main verification function."""
    print("🔍 SOLANA WALLET VERIFIER")
    print("=" * 50)
    
    # Load addresses from file
    addresses = load_addresses_from_file()
    
    if not addresses:
        print("❌ No addresses to verify")
        return
    
    try:
        async with WalletVerifier() as verifier:
            results = await verifier.verify_multiple_wallets(addresses)
            
            # Summary
            print("\n" + "=" * 60)
            print("📊 VERIFICATION SUMMARY")
            print("=" * 60)
            
            active_count = len([r for r in results if r['status'] == 'ACTIVE'])
            inactive_count = len([r for r in results if r['status'] == 'INACTIVE'])
            error_count = len([r for r in results if r['status'].startswith('ERROR')])
            not_found_count = len([r for r in results if r['status'] == 'NOT_FOUND'])
            
            print(f"✅ Active wallets: {active_count}")
            print(f"💤 Inactive wallets: {inactive_count}")
            print(f"❌ Not found: {not_found_count}")
            print(f"⚠️ Errors: {error_count}")
            
            # Show active wallets
            active_wallets = [r for r in results if r['status'] == 'ACTIVE']
            if active_wallets:
                print(f"\n🎯 ACTIVE WALLETS ({len(active_wallets)}):")
                for wallet in active_wallets:
                    print(f"   📍 {wallet['address']}")
                    print(f"   💰 Balance: {wallet['balance_sol']:.4f} SOL")
                    print(f"   🔗 {wallet['solscan_url']}")
                    print()
            
            # Save results
            with open('wallet_verification_results.json', 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"💾 Results saved to 'wallet_verification_results.json'")
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
