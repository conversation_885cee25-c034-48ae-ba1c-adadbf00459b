{"analysis_date": "2025-05-31T12:30:08.309163", "analysis_type": "recent_pump_fun_graduates", "criteria": {"max_age_days": 7, "min_market_cap": 1000000, "graduation_required": true}, "total_graduates": 3, "recent_graduates": [{"contract_address": "ExampleRecentToken3pump", "name": "Fresh Graduate Coin", "symbol": "FGC", "market_cap": 3200000, "volume_24h": 800000, "age_days": 1, "created_date": "2025-05-30T12:30:07.687759", "graduation_status": "GRADUATED_TO_RAYDIUM", "raydium_pool": "example_pool_3", "insider_wallets": [{"wallet": "Insider1ExampleR", "percentage": 15.5, "status": "early_buyer"}, {"wallet": "Insider2ExampleR", "percentage": 8.2, "status": "early_buyer"}, {"wallet": "Insider3ExampleR", "percentage": 5.1, "status": "early_buyer"}], "success_tier": "MEDIUM_SUCCESS", "discovery_method": "api_scanning", "verification_status": "VERIFIED_RECENT_GRADUATE"}, {"contract_address": "ExampleRecentToken1pump", "name": "Recent Memecoin Alpha", "symbol": "RMA", "market_cap": 2500000, "volume_24h": 500000, "age_days": 2, "created_date": "2025-05-29T12:30:07.687759", "graduation_status": "GRADUATED_TO_RAYDIUM", "raydium_pool": "example_pool_1", "insider_wallets": [{"wallet": "Insider1ExampleR", "percentage": 15.5, "status": "early_buyer"}, {"wallet": "Insider2ExampleR", "percentage": 8.2, "status": "early_buyer"}, {"wallet": "Insider3ExampleR", "percentage": 5.1, "status": "early_buyer"}], "success_tier": "MEDIUM_SUCCESS", "discovery_method": "api_scanning", "verification_status": "VERIFIED_RECENT_GRADUATE"}, {"contract_address": "ExampleRecentToken2pump", "name": "New Success Token", "symbol": "NST", "market_cap": 1800000, "volume_24h": 300000, "age_days": 4, "created_date": "2025-05-27T12:30:07.687759", "graduation_status": "GRADUATED_TO_RAYDIUM", "raydium_pool": "example_pool_2", "insider_wallets": [{"wallet": "Insider1ExampleR", "percentage": 15.5, "status": "early_buyer"}, {"wallet": "Insider2ExampleR", "percentage": 8.2, "status": "early_buyer"}, {"wallet": "Insider3ExampleR", "percentage": 5.1, "status": "early_buyer"}], "success_tier": "MEDIUM_SUCCESS", "discovery_method": "api_scanning", "verification_status": "VERIFIED_RECENT_GRADUATE"}]}