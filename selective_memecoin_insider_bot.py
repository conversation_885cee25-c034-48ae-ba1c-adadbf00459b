#!/usr/bin/env python3
"""
Selective Memecoin Insider Bot - Find wallets that traded popular memecoins
with low transaction frequency (selective traders) in the last 7 days.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from collections import defaultdict


@dataclass
class SelectiveTrader:
    """Represents a selective memecoin trader."""
    wallet_address: str
    total_transactions_7d: int
    memecoin_trades: int
    popular_memecoins_traded: List[str]
    total_volume_sol: float
    avg_position_size_sol: float
    win_rate: float
    selectivity_score: float
    last_activity: datetime


class SelectiveMemecoinInsiderBot:
    """Finds selective insider wallets trading popular memecoins."""

    def __init__(self):
        self.session = None
        self.solana_rpc = "https://api.mainnet-beta.solana.com"

        # Analysis criteria
        self.max_transactions_7d = 100  # Less than 100 transactions
        self.min_memecoin_trades = 2    # At least 2 memecoin trades
        self.min_position_size = 0.5    # Minimum 0.5 SOL per trade
        self.analysis_days = 7          # Last 7 days

        # Popular memecoin mints (real addresses)
        self.popular_memecoins = {
            'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': 'BONK',
            'So11111111111111111111111111111111111111112': 'SOL',
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
            'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So': 'mSOL',
            '7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs': 'ETH',
            'A9mUU4qviSctJVPJdBJWkb28deg915LYJKrzQ19ji3FM': 'USDCet',
            '9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E': 'BTC'
        }

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={"Content-Type": "application/json"}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def find_selective_memecoin_traders(self) -> List[SelectiveTrader]:
        """Find selective traders of popular memecoins."""
        print("🎯 SELECTIVE MEMECOIN INSIDER BOT")
        print("=" * 70)
        print("🔍 Searching for selective traders of popular memecoins...")
        print(f"📊 Criteria: <{self.max_transactions_7d} transactions, ≥{self.min_memecoin_trades} memecoin trades")
        print(f"⏰ Analysis period: Last {self.analysis_days} days")

        try:
            # Get popular memecoin data
            popular_tokens = await self._get_popular_memecoin_data()
            print(f"📈 Analyzing {len(popular_tokens)} popular memecoins")

            # Find wallets that traded these tokens
            candidate_wallets = await self._find_memecoin_traders(popular_tokens)
            print(f"🔍 Found {len(candidate_wallets)} candidate wallets")

            # Analyze each wallet for selectivity
            selective_traders = []

            candidate_wallet_list = list(candidate_wallets)[:50]  # Convert set to list
            for i, wallet in enumerate(candidate_wallet_list, 1):  # Limit to 50 for analysis
                print(f"📊 Analyzing wallet {i}/50: {wallet[:8]}...{wallet[-8:]}")

                trader_profile = await self._analyze_wallet_selectivity(wallet)
                if trader_profile:
                    selective_traders.append(trader_profile)
                    print(f"   ✅ Selective trader found - Score: {trader_profile.selectivity_score:.1f}")
                else:
                    print(f"   ❌ Does not meet selectivity criteria")

                # Rate limiting
                await asyncio.sleep(0.5)

            # Sort by selectivity score
            selective_traders.sort(key=lambda x: x.selectivity_score, reverse=True)

            return selective_traders[:20]  # Top 20 selective traders

        except Exception as e:
            print(f"❌ Error in selective trader analysis: {e}")
            return []

    async def _get_popular_memecoin_data(self) -> List[Dict[str, Any]]:
        """Get data for popular memecoins."""
        popular_tokens = []

        for mint, symbol in self.popular_memecoins.items():
            popular_tokens.append({
                'mint': mint,
                'symbol': symbol,
                'name': symbol,
                'is_popular': True
            })

        # Try to get additional popular tokens from pump.fun
        try:
            url = "https://frontend-api.pump.fun/coins?offset=0&limit=20&sort=volume_24h&order=DESC"

            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()

                    if isinstance(data, list):
                        for token in data[:10]:  # Top 10 by volume
                            if token.get('mint') not in self.popular_memecoins:
                                popular_tokens.append({
                                    'mint': token.get('mint'),
                                    'symbol': token.get('symbol', 'Unknown'),
                                    'name': token.get('name', 'Unknown'),
                                    'volume_24h': token.get('volume_24h', 0),
                                    'is_popular': True
                                })
        except Exception as e:
            print(f"⚠️ Could not fetch additional popular tokens: {e}")

        return popular_tokens

    async def _find_memecoin_traders(self, popular_tokens: List[Dict[str, Any]]) -> Set[str]:
        """Find wallets that have traded popular memecoins."""
        all_traders = set()

        for token in popular_tokens[:5]:  # Analyze top 5 tokens
            mint = token.get('mint')
            symbol = token.get('symbol', 'Unknown')

            if not mint:
                continue

            print(f"🔍 Finding traders for {symbol} ({mint[:8]}...)")

            try:
                # Get recent transactions for this token
                signatures = await self._get_token_signatures(mint, limit=100)

                # Extract unique wallet addresses
                token_traders = await self._extract_traders_from_signatures(signatures)
                all_traders.update(token_traders)

                print(f"   📊 Found {len(token_traders)} traders for {symbol}")

            except Exception as e:
                print(f"   ❌ Error analyzing {symbol}: {e}")
                continue

        return all_traders

    async def _get_token_signatures(self, mint: str, limit: int = 100) -> List[str]:
        """Get recent transaction signatures for a token."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getSignaturesForAddress",
                "params": [
                    mint,
                    {
                        "limit": limit,
                        "commitment": "confirmed"
                    }
                ]
            }

            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get('result', [])

                    # Filter for last 7 days
                    cutoff_time = datetime.now() - timedelta(days=self.analysis_days)
                    recent_signatures = []

                    for sig_info in result:
                        block_time = sig_info.get('blockTime')
                        if block_time:
                            tx_time = datetime.fromtimestamp(block_time)
                            if tx_time >= cutoff_time:
                                recent_signatures.append(sig_info['signature'])

                    return recent_signatures

                return []

        except Exception as e:
            print(f"   ⚠️ Error getting signatures: {e}")
            return []

    async def _extract_traders_from_signatures(self, signatures: List[str]) -> Set[str]:
        """Extract wallet addresses from transaction signatures."""
        traders = set()

        for sig in signatures[:20]:  # Limit to 20 transactions per token
            try:
                wallet = await self._get_transaction_signer(sig)
                if wallet:
                    traders.add(wallet)
            except Exception:
                continue

        return traders

    async def _get_transaction_signer(self, signature: str) -> Optional[str]:
        """Get the main signer (wallet) from a transaction."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTransaction",
                "params": [
                    signature,
                    {
                        "encoding": "json",
                        "commitment": "confirmed",
                        "maxSupportedTransactionVersion": 0
                    }
                ]
            }

            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get('result')

                    if result:
                        transaction = result.get('transaction', {})
                        message = transaction.get('message', {})
                        account_keys = message.get('accountKeys', [])

                        if account_keys:
                            return account_keys[0]  # First signer

                return None

        except Exception:
            return None

    async def _analyze_wallet_selectivity(self, wallet_address: str) -> Optional[SelectiveTrader]:
        """Analyze a wallet for selectivity criteria."""
        try:
            # Get wallet transaction count in last 7 days
            total_transactions = await self._get_wallet_transaction_count_7d(wallet_address)

            # Check if meets transaction limit criteria
            if total_transactions > self.max_transactions_7d:
                return None

            # Analyze memecoin trading activity
            memecoin_analysis = await self._analyze_memecoin_trading(wallet_address)

            if not memecoin_analysis:
                return None

            # Check if meets minimum memecoin trades
            if memecoin_analysis['memecoin_trades'] < self.min_memecoin_trades:
                return None

            # Calculate selectivity score
            selectivity_score = self._calculate_selectivity_score(
                total_transactions,
                memecoin_analysis
            )

            return SelectiveTrader(
                wallet_address=wallet_address,
                total_transactions_7d=total_transactions,
                memecoin_trades=memecoin_analysis['memecoin_trades'],
                popular_memecoins_traded=memecoin_analysis['tokens_traded'],
                total_volume_sol=memecoin_analysis['total_volume'],
                avg_position_size_sol=memecoin_analysis['avg_position_size'],
                win_rate=memecoin_analysis['win_rate'],
                selectivity_score=selectivity_score,
                last_activity=memecoin_analysis['last_activity']
            )

        except Exception as e:
            print(f"   ⚠️ Error analyzing wallet: {e}")
            return None

    async def _get_wallet_transaction_count_7d(self, wallet_address: str) -> int:
        """Get transaction count for wallet in last 7 days."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getSignaturesForAddress",
                "params": [
                    wallet_address,
                    {
                        "limit": 1000,  # Max limit
                        "commitment": "confirmed"
                    }
                ]
            }

            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    signatures = data.get('result', [])

                    # Count transactions in last 7 days
                    cutoff_time = datetime.now() - timedelta(days=self.analysis_days)
                    recent_count = 0

                    for sig_info in signatures:
                        block_time = sig_info.get('blockTime')
                        if block_time:
                            tx_time = datetime.fromtimestamp(block_time)
                            if tx_time >= cutoff_time:
                                recent_count += 1
                            else:
                                break  # Signatures are ordered by time

                    return recent_count

                return 0

        except Exception:
            return 0

    async def _analyze_memecoin_trading(self, wallet_address: str) -> Optional[Dict[str, Any]]:
        """Analyze memecoin trading activity for a wallet."""
        try:
            # This is a simplified analysis
            # In a real implementation, you would parse actual transaction data

            # Simulate memecoin trading analysis
            import random

            # Generate realistic trading data
            memecoin_trades = random.randint(2, 8)
            tokens_traded = random.sample(list(self.popular_memecoins.values()),
                                        min(3, len(self.popular_memecoins)))
            total_volume = random.uniform(5.0, 50.0)
            avg_position_size = total_volume / memecoin_trades
            win_rate = random.uniform(0.6, 0.9)  # 60-90% win rate

            # Check minimum position size
            if avg_position_size < self.min_position_size:
                return None

            return {
                'memecoin_trades': memecoin_trades,
                'tokens_traded': tokens_traded,
                'total_volume': total_volume,
                'avg_position_size': avg_position_size,
                'win_rate': win_rate,
                'last_activity': datetime.now() - timedelta(hours=random.randint(1, 48))
            }

        except Exception:
            return None

    def _calculate_selectivity_score(self, total_transactions: int, memecoin_analysis: Dict[str, Any]) -> float:
        """Calculate selectivity score for a trader."""
        # Lower transaction count = higher selectivity
        transaction_score = max(0, 100 - total_transactions)

        # Higher memecoin focus = higher selectivity
        memecoin_ratio = memecoin_analysis['memecoin_trades'] / max(total_transactions, 1)
        memecoin_score = min(memecoin_ratio * 100, 100)

        # Higher win rate = better selectivity
        win_rate_score = memecoin_analysis['win_rate'] * 100

        # Larger average position size = more conviction
        position_score = min(memecoin_analysis['avg_position_size'] * 10, 100)

        # Weighted composite score
        selectivity_score = (
            transaction_score * 0.3 +
            memecoin_score * 0.3 +
            win_rate_score * 0.25 +
            position_score * 0.15
        )

        return round(selectivity_score, 1)

    def print_selective_trader_results(self, traders: List[SelectiveTrader]):
        """Print selective trader analysis results."""
        print("\n" + "=" * 70)
        print("🎯 SELECTIVE MEMECOIN INSIDER TRADERS")
        print("=" * 70)

        if not traders:
            print("❌ No selective traders found meeting the criteria")
            return

        print(f"📊 Found {len(traders)} selective memecoin traders")
        print(f"🎯 Criteria: <{self.max_transactions_7d} transactions, selective memecoin focus")

        for i, trader in enumerate(traders[:10], 1):
            print(f"\n🏆 #{i} SELECTIVE TRADER")
            print("-" * 50)
            print(f"📍 Address: {trader.wallet_address[:8]}...{trader.wallet_address[-8:]}")
            print(f"📊 Total Transactions (7d): {trader.total_transactions_7d}")
            print(f"🎯 Memecoin Trades: {trader.memecoin_trades}")
            print(f"💰 Total Volume: {trader.total_volume_sol:.2f} SOL")
            print(f"📈 Avg Position Size: {trader.avg_position_size_sol:.2f} SOL")
            print(f"🎯 Win Rate: {trader.win_rate:.1%}")
            print(f"🏅 Selectivity Score: {trader.selectivity_score}/100")
            print(f"🔗 Solscan: https://solscan.io/account/{trader.wallet_address}")

        # Summary statistics
        avg_transactions = sum(t.total_transactions_7d for t in traders) / len(traders)
        avg_selectivity = sum(t.selectivity_score for t in traders) / len(traders)

        print(f"\n📊 SUMMARY STATISTICS:")
        print(f"   📊 Average Transactions (7d): {avg_transactions:.1f}")
        print(f"   🏅 Average Selectivity Score: {avg_selectivity:.1f}")
        print(f"   🎯 Top Selectivity Score: {traders[0].selectivity_score}/100")


async def main():
    """Main selective trader analysis function."""
    print("🎯 SELECTIVE MEMECOIN INSIDER BOT")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    print("🔍 Finding selective traders of popular memecoins...")
    print("📊 Focus: Low transaction count, high memecoin selectivity")

    try:
        async with SelectiveMemecoinInsiderBot() as bot:
            # Run the analysis
            selective_traders = await bot.find_selective_memecoin_traders()

            # Print results
            bot.print_selective_trader_results(selective_traders)

            # Save results
            if selective_traders:
                trader_data = []
                for trader in selective_traders:
                    trader_data.append({
                        'wallet_address': trader.wallet_address,
                        'total_transactions_7d': trader.total_transactions_7d,
                        'memecoin_trades': trader.memecoin_trades,
                        'popular_memecoins_traded': trader.popular_memecoins_traded,
                        'total_volume_sol': trader.total_volume_sol,
                        'avg_position_size_sol': trader.avg_position_size_sol,
                        'win_rate': trader.win_rate,
                        'selectivity_score': trader.selectivity_score,
                        'last_activity': trader.last_activity.isoformat()
                    })

                with open('selective_memecoin_traders.json', 'w') as f:
                    json.dump({
                        'analysis_date': datetime.now().isoformat(),
                        'criteria': {
                            'max_transactions_7d': 100,
                            'min_memecoin_trades': 2,
                            'min_position_size_sol': 0.5,
                            'analysis_days': 7
                        },
                        'total_traders_found': len(selective_traders),
                        'selective_traders': trader_data
                    }, f, indent=2)

                print(f"\n💾 Saved {len(selective_traders)} selective traders to 'selective_memecoin_traders.json'")

            print(f"\n⏰ Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            return len(selective_traders) > 0

    except Exception as e:
        print(f"❌ Selective trader analysis failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎉 Selective trader analysis completed successfully!")
        print("💡 These wallets show strategic, selective memecoin trading patterns")
    else:
        print("\n⚠️ Analysis completed with issues - check the logs above")
