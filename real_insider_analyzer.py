#!/usr/bin/env python3
"""
Real Insider Wallet Analyzer - Connects to actual Solana blockchain data.
Finds real wallets that made profitable early trades on pump.fun tokens.
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import time


@dataclass
class RealInsiderTrade:
    """Represents a real insider trade from blockchain data."""
    wallet_address: str
    token_mint: str
    token_name: str
    token_symbol: str
    signature: str
    entry_time: datetime
    amount_sol: float
    token_amount: float
    price_per_token: float
    minutes_after_launch: float
    current_price: Optional[float] = None
    current_value_sol: Optional[float] = None
    unrealized_pnl_sol: Optional[float] = None
    roi_percentage: Optional[float] = None


class RealInsiderAnalyzer:
    """Analyzes real blockchain data for insider trading patterns."""
    
    def __init__(self):
        self.session = None
        self.solana_rpc = "https://api.mainnet-beta.solana.com"
        self.pump_fun_program = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
        
        # Real analysis thresholds
        self.early_entry_threshold_minutes = 15  # More realistic for real data
        self.min_position_size_sol = 0.1
        self.min_roi_threshold = 100  # 100% minimum for real insider detection
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={"Content-Type": "application/json"}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def find_real_insider_wallets(self) -> List[str]:
        """Find real insider wallets from recent pump.fun launches."""
        print("🔍 Analyzing REAL Solana blockchain data for insider wallets...")
        print("=" * 80)
        
        try:
            # Get recent pump.fun tokens from real API
            recent_tokens = await self._get_real_pump_tokens()
            print(f"📊 Found {len(recent_tokens)} real tokens to analyze")
            
            all_insider_trades = []
            
            for i, token in enumerate(recent_tokens[:10], 1):  # Limit to 10 for demo
                print(f"\n🔍 Analyzing token {i}/10: {token.get('name', 'Unknown')}")
                
                # Get real early transactions for this token
                early_trades = await self._get_real_early_trades(token)
                
                if early_trades:
                    print(f"   💰 Found {len(early_trades)} early trades")
                    all_insider_trades.extend(early_trades)
                else:
                    print(f"   ⚠️ No early trades found")
                
                # Rate limiting for API calls
                await asyncio.sleep(1)
            
            # Analyze trades for insider patterns
            insider_wallets = self._identify_insider_wallets(all_insider_trades)
            
            return insider_wallets
            
        except Exception as e:
            print(f"❌ Error in real analysis: {e}")
            return []
    
    async def _get_real_pump_tokens(self) -> List[Dict[str, Any]]:
        """Get real pump.fun tokens from their API."""
        try:
            # Use pump.fun's real API endpoint
            url = "https://frontend-api.pump.fun/coins?offset=0&limit=50&sort=created_timestamp&order=DESC"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data if isinstance(data, list) else []
                else:
                    print(f"⚠️ API returned status {response.status}")
                    return []
                    
        except Exception as e:
            print(f"❌ Error fetching real tokens: {e}")
            return []
    
    async def _get_real_early_trades(self, token: Dict[str, Any]) -> List[RealInsiderTrade]:
        """Get real early trades for a token using Solana RPC."""
        mint = token.get('mint')
        if not mint:
            return []
        
        try:
            # Get token creation time
            created_timestamp = token.get('created_timestamp', 0)
            if created_timestamp > 1e10:
                created_timestamp = created_timestamp / 1000
            
            launch_time = datetime.fromtimestamp(created_timestamp)
            cutoff_time = launch_time + timedelta(minutes=self.early_entry_threshold_minutes)
            
            # Get signatures for this token (simplified approach)
            signatures = await self._get_token_signatures(mint, launch_time, cutoff_time)
            
            early_trades = []
            for sig in signatures[:20]:  # Limit to first 20 transactions
                trade = await self._parse_transaction_for_trade(sig, token, launch_time)
                if trade:
                    early_trades.append(trade)
            
            return early_trades
            
        except Exception as e:
            print(f"   ❌ Error getting early trades for {mint}: {e}")
            return []
    
    async def _get_token_signatures(self, mint: str, launch_time: datetime, cutoff_time: datetime) -> List[str]:
        """Get transaction signatures for a token using Solana RPC."""
        try:
            # This is a simplified approach - in production you'd use more sophisticated methods
            # like monitoring specific programs or using specialized indexers
            
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getSignaturesForAddress",
                "params": [
                    mint,
                    {
                        "limit": 100,
                        "commitment": "confirmed"
                    }
                ]
            }
            
            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get('result', [])
                    
                    # Filter by time (approximate)
                    signatures = []
                    for sig_info in result:
                        # Note: This is simplified - real implementation would need better time filtering
                        signatures.append(sig_info['signature'])
                    
                    return signatures[:50]  # Return first 50
                else:
                    return []
                    
        except Exception as e:
            print(f"   ⚠️ Error getting signatures: {e}")
            return []
    
    async def _parse_transaction_for_trade(self, signature: str, token: Dict[str, Any], launch_time: datetime) -> Optional[RealInsiderTrade]:
        """Parse a transaction to extract trade information."""
        try:
            # Get transaction details
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTransaction",
                "params": [
                    signature,
                    {
                        "encoding": "json",
                        "commitment": "confirmed",
                        "maxSupportedTransactionVersion": 0
                    }
                ]
            }
            
            async with self.session.post(self.solana_rpc, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get('result')
                    
                    if not result:
                        return None
                    
                    # Extract trade information (simplified)
                    # In production, you'd parse the transaction instructions properly
                    meta = result.get('meta', {})
                    transaction = result.get('transaction', {})
                    
                    # Get block time
                    block_time = result.get('blockTime')
                    if not block_time:
                        return None
                    
                    trade_time = datetime.fromtimestamp(block_time)
                    minutes_after_launch = (trade_time - launch_time).total_seconds() / 60
                    
                    # Skip if not early enough
                    if minutes_after_launch > self.early_entry_threshold_minutes:
                        return None
                    
                    # Extract wallet address (first signer)
                    message = transaction.get('message', {})
                    account_keys = message.get('accountKeys', [])
                    
                    if not account_keys:
                        return None
                    
                    wallet_address = account_keys[0]
                    
                    # Extract amounts (simplified - would need proper parsing)
                    # This is a placeholder - real implementation would parse instruction data
                    pre_balances = meta.get('preBalances', [])
                    post_balances = meta.get('postBalances', [])
                    
                    if len(pre_balances) > 0 and len(post_balances) > 0:
                        sol_change = (post_balances[0] - pre_balances[0]) / 1e9  # Convert lamports to SOL
                        
                        # Only consider buys (negative SOL change)
                        if sol_change >= -self.min_position_size_sol:
                            return None
                        
                        amount_sol = abs(sol_change)
                        
                        return RealInsiderTrade(
                            wallet_address=wallet_address,
                            token_mint=token.get('mint', ''),
                            token_name=token.get('name', 'Unknown'),
                            token_symbol=token.get('symbol', 'Unknown'),
                            signature=signature,
                            entry_time=trade_time,
                            amount_sol=amount_sol,
                            token_amount=0,  # Would need to parse from transaction
                            price_per_token=0,  # Would calculate from amounts
                            minutes_after_launch=minutes_after_launch
                        )
                    
                    return None
                    
        except Exception as e:
            print(f"   ⚠️ Error parsing transaction {signature}: {e}")
            return None
    
    def _identify_insider_wallets(self, trades: List[RealInsiderTrade]) -> List[str]:
        """Identify wallets with insider trading patterns."""
        if not trades:
            return []
        
        # Group trades by wallet
        wallet_trades = {}
        for trade in trades:
            if trade.wallet_address not in wallet_trades:
                wallet_trades[trade.wallet_address] = []
            wallet_trades[trade.wallet_address].append(trade)
        
        insider_wallets = []
        
        for wallet_address, wallet_trades_list in wallet_trades.items():
            # Analyze wallet for insider patterns
            if self._is_insider_wallet(wallet_trades_list):
                insider_wallets.append(wallet_address)
        
        return insider_wallets
    
    def _is_insider_wallet(self, trades: List[RealInsiderTrade]) -> bool:
        """Determine if a wallet shows insider trading patterns."""
        if len(trades) < 2:  # Need multiple trades
            return False
        
        # Check for consistent early entries
        early_trades = [t for t in trades if t.minutes_after_launch <= 5]
        if len(early_trades) < len(trades) * 0.7:  # 70% of trades should be very early
            return False
        
        # Check for significant position sizes
        avg_position = sum(t.amount_sol for t in trades) / len(trades)
        if avg_position < self.min_position_size_sol * 2:  # Above average positions
            return False
        
        return True


async def main():
    """Main function to run real insider analysis."""
    print("🚀 REAL SOLANA INSIDER WALLET ANALYZER")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    print("🔗 Connecting to real Solana blockchain data...")
    print("📊 This may take a few minutes to analyze real transactions...")
    
    try:
        async with RealInsiderAnalyzer() as analyzer:
            insider_wallets = await analyzer.find_real_insider_wallets()
            
            print("\n" + "=" * 80)
            print("🎯 REAL INSIDER WALLET ANALYSIS RESULTS")
            print("=" * 80)
            
            if insider_wallets:
                print(f"✅ Found {len(insider_wallets)} potential insider wallets:")
                
                for i, wallet in enumerate(insider_wallets, 1):
                    print(f"\n🏆 #{i} REAL INSIDER WALLET:")
                    print(f"📍 Address: {wallet}")
                    print(f"🔗 Solscan: https://solscan.io/account/{wallet}")
                    
                # Save real wallets to file
                with open('real_insider_wallets.txt', 'w') as f:
                    f.write("# Real Insider Wallet Addresses\n")
                    f.write(f"# Generated: {datetime.now().isoformat()}\n")
                    f.write(f"# Total Found: {len(insider_wallets)}\n\n")
                    
                    for wallet in insider_wallets:
                        f.write(f"{wallet}\n")
                
                print(f"\n✅ Saved {len(insider_wallets)} real wallet addresses to 'real_insider_wallets.txt'")
                
            else:
                print("❌ No insider wallets found with current criteria")
                print("💡 Try adjusting thresholds or analyzing more tokens")
            
            print(f"\n⏰ Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
    except Exception as e:
        print(f"❌ Real analysis failed: {e}")
        print("💡 This requires access to Solana RPC and may need API keys for production use")


if __name__ == "__main__":
    asyncio.run(main())
