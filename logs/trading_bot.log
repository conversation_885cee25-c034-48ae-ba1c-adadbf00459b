2025-05-25 17:54:55,737 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:55.736854Z"}
2025-05-25 17:54:56,397 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:54:56.397454Z"}
2025-05-25 17:54:56,403 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.403735Z"}
2025-05-25 17:54:56,413 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.412323Z"}
2025-05-25 17:54:56,447 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:54:56.447070Z"}
2025-05-25 17:54:56,487 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:54:56.487287Z"}
2025-05-25 17:54:56,498 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:54:56.498359Z"}
2025-05-25 17:54:56,516 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:54:56.516584Z"}
2025-05-25 17:54:56,528 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:54:56.528819Z"}
2025-05-25 17:54:56,537 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.537572Z"}
2025-05-25 17:54:56,543 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:54:56.543847Z"}
2025-05-25 17:54:56,550 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.550749Z"}
2025-05-25 17:54:56,560 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:54:56.560229Z"}
2025-05-25 17:54:56,566 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:54:56.566354Z"}
2025-05-25 17:54:56,573 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:54:56.572971Z"}
2025-05-25 17:56:10,941 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:10.941571Z"}
2025-05-25 17:56:11,118 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:56:11.118560Z"}
2025-05-25 17:56:11,124 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.124063Z"}
2025-05-25 17:56:11,129 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.129341Z"}
2025-05-25 17:56:11,135 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T21:56:11.135574Z"}
2025-05-25 17:56:11,150 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T21:56:11.150751Z"}
2025-05-25 17:56:11,191 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T21:56:11.191162Z"}
2025-05-25 17:56:11,219 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T21:56:11.219027Z"}
2025-05-25 17:56:11,235 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T21:56:11.235235Z"}
2025-05-25 17:56:11,248 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.248662Z"}
2025-05-25 17:56:11,276 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T21:56:11.276372Z"}
2025-05-25 17:56:11,289 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.289307Z"}
2025-05-25 17:56:11,301 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:56:11.301738Z"}
2025-05-25 17:56:11,311 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T21:56:11.311540Z"}
2025-05-25 17:56:11,315 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T21:56:11.315073Z"}
2025-05-25 17:57:35,285 - test_bot - INFO - {"event": "\u23f0 Test started at: 2025-05-25 17:57:35", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.285544Z"}
2025-05-25 17:57:35,294 - test_bot - INFO - {"event": "\ud83d\ude80 Starting Minimal Solana Trading Bot Test...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.293997Z"}
2025-05-25 17:57:35,298 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.298890Z"}
2025-05-25 17:57:35,318 - test_bot - INFO - {"event": "\ud83d\udcca Testing database connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.318141Z"}
2025-05-25 17:57:35,423 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T21:57:35.423852Z"}
2025-05-25 17:57:35,426 - test_bot - INFO - {"event": "\u2705 Database initialized successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.426162Z"}
2025-05-25 17:57:35,428 - test_bot - INFO - {"event": "\u2699\ufe0f Testing configuration...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.428409Z"}
2025-05-25 17:57:35,439 - test_bot - INFO - {"event": "RPC URL: https://api.mainnet-beta.solana.com", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.439508Z"}
2025-05-25 17:57:35,441 - test_bot - INFO - {"event": "Pump.fun monitoring: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.441886Z"}
2025-05-25 17:57:35,444 - test_bot - INFO - {"event": "Copy trading: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.444079Z"}
2025-05-25 17:57:35,476 - test_bot - INFO - {"event": "Telegram bot: True", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.476631Z"}
2025-05-25 17:57:35,479 - test_bot - INFO - {"event": "\u2705 Configuration loaded successfully", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.478979Z"}
2025-05-25 17:57:35,489 - test_bot - INFO - {"event": "\ud83d\ude80 Testing Pump.fun API connection...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.489327Z"}
2025-05-25 17:57:35,493 - test_bot - ERROR - {"event": "\u274c Pump.fun API test failed: 'PumpFunMonitor' object has no attribute 'initialize'", "logger": "test_bot", "level": "error", "timestamp": "2025-05-25T21:57:35.493535Z"}
2025-05-25 17:57:35,510 - test_bot - INFO - {"event": "\u23f1\ufe0f Running monitoring test for 30 seconds...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:57:35.509941Z"}
2025-05-25 17:57:35,519 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:57:35.519199Z"}
2025-05-25 17:57:35,670 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:35.669959Z"}
2025-05-25 17:57:40,754 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:40.754005Z"}
2025-05-25 17:57:45,838 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:45.838399Z"}
2025-05-25 17:57:50,923 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:50.923363Z"}
2025-05-25 17:57:56,042 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:57:56.042403Z"}
2025-05-25 17:58:01,133 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T21:58:01.133000Z"}
2025-05-25 17:58:05,521 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.521289Z"}
2025-05-25 17:58:05,524 - test_bot - INFO - {"event": "\u2705 Monitoring test completed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.523943Z"}
2025-05-25 17:58:05,526 - test_bot - INFO - {"event": "\ud83e\uddf9 Cleaning up...", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.526420Z"}
2025-05-25 17:58:05,541 - test_bot - INFO - {"event": "\u2705 Database connections closed", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.541837Z"}
2025-05-25 17:58:05,555 - test_bot - INFO - {"event": "============================================================", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.555592Z"}
2025-05-25 17:58:05,557 - test_bot - INFO - {"event": "\ud83c\udf89 All core functionality tests passed!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.557839Z"}
2025-05-25 17:58:05,568 - test_bot - INFO - {"event": "\u2705 The Solana Trading Bot core is working correctly", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.563329Z"}
2025-05-25 17:58:05,577 - test_bot - INFO - {"event": "\n\ud83d\ude80 CORE FUNCTIONALITY TEST: PASSED", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.577430Z"}
2025-05-25 17:58:05,591 - test_bot - INFO - {"event": "The bot's core systems are working correctly!", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.591562Z"}
2025-05-25 17:58:05,623 - test_bot - INFO - {"event": "You can now run the full bot with: python run_bot.py", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.623162Z"}
2025-05-25 17:58:05,625 - test_bot - INFO - {"event": "\u23f0 Test completed at: 2025-05-25 17:58:05", "logger": "test_bot", "level": "info", "timestamp": "2025-05-25T21:58:05.625613Z"}
2025-05-25 17:58:05,640 - PumpFunMonitor - INFO - {"event": "New token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.640499Z"}
2025-05-25 17:58:05,652 - PumpFunMonitor - INFO - {"event": "Trending token monitoring cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.652611Z"}
2025-05-25 17:58:05,658 - PumpFunMonitor - INFO - {"event": "Cleanup task cancelled", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T21:58:05.658466Z"}
2025-05-25 18:46:31,456 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.456573Z"}
2025-05-25 18:46:31,622 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:46:31.622346Z"}
2025-05-25 18:46:31,625 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.625427Z"}
2025-05-25 18:46:31,627 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.627806Z"}
2025-05-25 18:46:31,630 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:46:31.630520Z"}
2025-05-25 18:46:31,655 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-25T22:46:31.655722Z"}
2025-05-25 18:46:31,663 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-25T22:46:31.663001Z"}
2025-05-25 18:46:31,742 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-25T22:46:31.741884Z"}
2025-05-25 18:46:31,774 - solana_trading_bot - ERROR - {"event": "Bot crashed: AsyncClient.__init__() got an unexpected keyword argument 'proxy'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-25T22:46:31.763601Z"}
2025-05-25 18:46:31,781 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.781163Z"}
2025-05-25 18:46:31,820 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-25T22:46:31.820381Z"}
2025-05-25 18:46:31,869 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.869355Z"}
2025-05-25 18:46:31,894 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:46:31.894049Z"}
2025-05-25 18:46:31,905 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-25T22:46:31.905015Z"}
2025-05-25 18:46:31,935 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:46:31.935541Z"}
2025-05-25 18:47:38,973 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.973327Z"}
2025-05-25 18:47:38,993 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:47:38.993781Z"}
2025-05-25 18:47:39,023 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.023435Z"}
2025-05-25 18:47:39,145 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:47:39.145619Z"}
2025-05-25 18:47:39,158 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.158521Z"}
2025-05-25 18:47:39,163 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.163394Z"}
2025-05-25 18:47:39,177 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:47:39.177044Z"}
2025-05-25 18:47:39,209 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.209601Z"}
2025-05-25 18:47:39,228 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.228144Z"}
2025-05-25 18:47:39,288 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.288079Z"}
2025-05-25 18:47:39,296 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.296179Z"}
2025-05-25 18:47:39,326 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.326022Z"}
2025-05-25 18:47:39,391 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.380255Z"}
2025-05-25 18:47:39,613 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:47:39.613500Z"}
2025-05-25 18:47:39,623 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:47:39.623665Z"}
2025-05-25 18:47:39,628 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:47:39.628676Z"}
2025-05-25 18:47:39,639 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.639707Z"}
2025-05-25 18:47:39,647 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.646932Z"}
2025-05-25 18:47:39,658 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.658237Z"}
2025-05-25 18:47:39,665 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:47:39.665154Z"}
2025-05-25 18:47:39,843 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:39.843811Z"}
2025-05-25 18:47:44,944 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:44.944444Z"}
2025-05-25 18:47:50,051 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:50.051812Z"}
2025-05-25 18:47:55,321 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:47:55.318947Z"}
2025-05-25 18:48:00,435 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:00.435812Z"}
2025-05-25 18:48:05,537 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:05.537077Z"}
2025-05-25 18:48:10,628 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:10.628524Z"}
2025-05-25 18:48:15,726 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:15.726263Z"}
2025-05-25 18:48:20,812 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:20.812546Z"}
2025-05-25 18:48:25,913 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:25.913146Z"}
2025-05-25 18:48:31,005 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:31.005795Z"}
2025-05-25 18:48:36,241 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:36.241753Z"}
2025-05-25 18:48:41,354 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:41.354621Z"}
2025-05-25 18:48:46,440 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:46.440156Z"}
2025-05-25 18:48:51,543 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:51.543398Z"}
2025-05-25 18:48:56,648 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:48:56.648632Z"}
2025-05-25 18:49:01,748 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:01.748897Z"}
2025-05-25 18:49:06,835 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:06.835114Z"}
2025-05-25 18:49:11,953 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:11.953167Z"}
2025-05-25 18:49:17,043 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:17.042975Z"}
2025-05-25 18:49:22,131 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:22.131905Z"}
2025-05-25 18:49:27,247 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:27.247879Z"}
2025-05-25 18:49:32,365 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:32.365722Z"}
2025-05-25 18:49:37,450 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:37.450813Z"}
2025-05-25 18:49:42,556 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:42.556507Z"}
2025-05-25 18:49:47,733 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:47.732157Z"}
2025-05-25 18:49:52,827 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:49:52.826994Z"}
2025-05-25 18:56:25,813 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:56:25.813040Z"}
2025-05-25 18:56:25,829 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T22:56:25.829854Z"}
2025-05-25 18:56:25,983 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:25.983491Z"}
2025-05-25 18:56:26,702 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T22:56:26.701900Z"}
2025-05-25 18:56:26,711 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.711457Z"}
2025-05-25 18:56:26,814 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.814161Z"}
2025-05-25 18:56:26,822 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T22:56:26.822377Z"}
2025-05-25 18:56:26,829 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.829242Z"}
2025-05-25 18:56:26,851 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:26.850483Z"}
2025-05-25 18:56:27,211 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.211016Z"}
2025-05-25 18:56:27,227 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.227622Z"}
2025-05-25 18:56:27,272 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.271913Z"}
2025-05-25 18:56:27,279 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.279753Z"}
2025-05-25 18:56:27,700 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T22:56:27.700315Z"}
2025-05-25 18:56:27,705 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T22:56:27.705214Z"}
2025-05-25 18:56:27,709 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T22:56:27.709864Z"}
2025-05-25 18:56:27,723 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.723839Z"}
2025-05-25 18:56:27,730 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.730528Z"}
2025-05-25 18:56:27,747 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.747738Z"}
2025-05-25 18:56:27,761 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T22:56:27.761294Z"}
2025-05-25 18:56:28,070 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:28.070259Z"}
2025-05-25 18:56:33,238 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:33.237884Z"}
2025-05-25 18:56:38,316 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:38.316707Z"}
2025-05-25 18:56:43,402 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:43.402283Z"}
2025-05-25 18:56:48,498 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:48.498285Z"}
2025-05-25 18:56:53,590 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:53.590497Z"}
2025-05-25 18:56:58,683 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:56:58.683726Z"}
2025-05-25 18:57:03,760 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:03.760435Z"}
2025-05-25 18:57:08,847 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:08.847174Z"}
2025-05-25 18:57:13,934 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:13.934447Z"}
2025-05-25 18:57:19,050 - PumpFunMonitor - WARNING - {"event": "API request failed: 503", "logger": "PumpFunMonitor", "level": "warning", "timestamp": "2025-05-25T22:57:19.050515Z"}
2025-05-25 19:01:30,242 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T23:01:30.242244Z"}
2025-05-25 19:01:30,256 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-25T23:01:30.256800Z"}
2025-05-25 19:01:30,260 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.260026Z"}
2025-05-25 19:01:30,540 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-25T23:01:30.540050Z"}
2025-05-25 19:01:30,544 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.544876Z"}
2025-05-25 19:01:30,588 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.587751Z"}
2025-05-25 19:01:30,624 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-25T23:01:30.624308Z"}
2025-05-25 19:01:30,631 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.631470Z"}
2025-05-25 19:01:30,687 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.687613Z"}
2025-05-25 19:01:30,745 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.744985Z"}
2025-05-25 19:01:30,776 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.776673Z"}
2025-05-25 19:01:30,809 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.809217Z"}
2025-05-25 19:01:30,837 - SolanaTradingBot - INFO - {"event": "Running 6 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:30.837682Z"}
2025-05-25 19:01:31,032 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-25T23:01:31.031999Z"}
2025-05-25 19:01:31,056 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-25T23:01:31.056726Z"}
2025-05-25 19:01:31,100 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.100850Z"}
2025-05-25 19:01:31,112 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.111346Z"}
2025-05-25 19:01:31,125 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.125518Z"}
2025-05-25 19:01:31,135 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.135681Z"}
2025-05-25 19:01:31,140 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-25T23:01:31.140644Z"}
2025-05-25 19:01:31,264 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.263406Z"}
2025-05-25 19:01:31,473 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.472968Z"}
2025-05-25 19:01:31,498 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:31.498262Z"}
2025-05-25 19:01:34,205 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DnP1YHAe... sold 0.2772 SOL of PLS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:34.205803Z"}
2025-05-25 19:01:38,710 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FSxwKvXC... sold 1.9802 SOL of nilsuisou", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:38.709945Z"}
2025-05-25 19:01:41,576 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HwUedgcj... sold 2.2300 SOL of BT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:41.576205Z"}
2025-05-25 19:01:43,996 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3KJWMTwq... sold 2.9703 SOL of biocompute", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:43.996669Z"}
2025-05-25 19:01:47,979 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GZVSEAaj... sold 2.9703 SOL of GOATSEUS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:47.979850Z"}
2025-05-25 19:01:48,265 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9h3CegiR... sold 2.2300 SOL of BIOCOMPUTE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:48.264022Z"}
2025-05-25 19:01:49,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BX5pa8eP... sold 2.9703 SOL of bc", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:49.024906Z"}
2025-05-25 19:01:51,404 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 74LgHx4z... sold 2.4752 SOL of truth", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:51.404226Z"}
2025-05-25 19:01:57,862 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GyiAMVLu... sold 1.9802 SOL of BIOPC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:57.862308Z"}
2025-05-25 19:01:58,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F7BtcvEr... sold 2.4752 SOL of SYBAU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:01:58.025858Z"}
2025-05-25 19:02:03,091 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: animeQsS... sold 2.9703 SOL of biocomp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:03.091694Z"}
2025-05-25 19:02:04,182 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FR4HKPjq... sold 0.9901 SOL of investment", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:04.182290Z"}
2025-05-25 19:02:04,999 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2zt4HPCD... sold 4.9505 SOL of VEO3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:04.998929Z"}
2025-05-25 19:02:08,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: qbrKg1oq... sold 2.8000 SOL of FreeWill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:08.241891Z"}
2025-05-25 19:02:09,558 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 28z37CM9... sold 0.1188 SOL of Alien", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:09.552782Z"}
2025-05-25 19:02:12,430 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8cztdSB5... sold 1.7822 SOL of Brucify ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:12.430532Z"}
2025-05-25 19:02:14,726 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EtV6GqY9... sold 1.4851 SOL of BULL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:14.725950Z"}
2025-05-25 19:02:14,957 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H1p7CyPF... sold 4.0000 SOL of Truth", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:14.957827Z"}
2025-05-25 19:02:15,224 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5heqWPem... sold 0.0010 SOL of GBIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:15.224122Z"}
2025-05-25 19:02:17,366 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6NiaaKRg... sold 1.4851 SOL of coinbase", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:17.366775Z"}
2025-05-25 19:02:19,378 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 2.4752 SOL of shell", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:19.373117Z"}
2025-05-25 19:02:22,131 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HwUedgcj... sold 2.2300 SOL of bt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:22.130403Z"}
2025-05-25 19:02:23,218 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4pMQrW7f... sold 0.9901 SOL of chopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:23.218559Z"}
2025-05-25 19:02:31,518 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CLVNpy5e... sold 0.0990 SOL of ENZO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:31.518577Z"}
2025-05-25 19:02:39,019 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4fC9hcfS... sold 0.9000 SOL of modl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:39.019897Z"}
2025-05-25 19:02:44,411 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H7JGZzpK... sold 0.9901 SOL of ndnp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:44.410935Z"}
2025-05-25 19:02:47,808 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GZVSEAaj... sold 2.9703 SOL of MEGA Coin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-25T23:02:47.808211Z"}
2025-05-26 13:15:06,774 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:06.773343Z"}
2025-05-26 13:15:07,412 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:15:07.412258Z"}
2025-05-26 13:15:07,445 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:07.444244Z"}
2025-05-26 13:15:07,547 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:07.545430Z"}
2025-05-26 13:15:07,606 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:15:07.606415Z"}
2025-05-26 13:15:07,750 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:15:07.750123Z"}
2025-05-26 13:15:08,062 - BloomBot - ERROR - {"event": "Failed to initialize Telegram bot: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "BloomBot", "level": "error", "timestamp": "2025-05-26T17:15:08.062333Z"}
2025-05-26 13:15:08,254 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-26T17:15:08.253984Z"}
2025-05-26 13:15:08,433 - solana_trading_bot - ERROR - {"event": "Bot crashed: 'BloomBot' object has no attribute 'unsubscribe_command'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-26T17:15:08.433688Z"}
2025-05-26 13:15:08,470 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:08.470165Z"}
2025-05-26 13:15:08,479 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-26T17:15:08.479701Z"}
2025-05-26 13:15:08,698 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-26T17:15:08.698152Z"}
2025-05-26 13:15:09,034 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:15:09.034682Z"}
2025-05-26 13:15:09,051 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:15:09.051848Z"}
2025-05-26 13:15:09,070 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:15:09.070560Z"}
2025-05-26 13:16:46,018 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.018110Z"}
2025-05-26 13:16:46,364 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:16:46.364222Z"}
2025-05-26 13:16:46,371 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.371060Z"}
2025-05-26 13:16:46,410 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:46.410257Z"}
2025-05-26 13:16:46,458 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:16:46.457938Z"}
2025-05-26 13:16:46,462 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:16:46.462412Z"}
2025-05-26 13:16:47,074 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 13:16:47,093 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:16:47.092961Z"}
2025-05-26 13:16:47,141 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: name 'CommandHandler' is not defined", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-26T17:16:47.141138Z"}
2025-05-26 13:16:47,173 - solana_trading_bot - ERROR - {"event": "Bot crashed: name 'CommandHandler' is not defined", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-26T17:16:47.173543Z"}
2025-05-26 13:16:47,185 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:47.184550Z"}
2025-05-26 13:16:47,199 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-26T17:16:47.198861Z"}
2025-05-26 13:16:47,208 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-26T17:16:47.208299Z"}
2025-05-26 13:16:47,216 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:16:47.216728Z"}
2025-05-26 13:16:47,223 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:16:47.223577Z"}
2025-05-26 13:16:47,237 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:16:47.237734Z"}
2025-05-26 13:18:19,800 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.800070Z"}
2025-05-26 13:18:19,953 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T17:18:19.953874Z"}
2025-05-26 13:18:19,961 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.961908Z"}
2025-05-26 13:18:19,964 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:19.964073Z"}
2025-05-26 13:18:19,966 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T17:18:19.966244Z"}
2025-05-26 13:18:19,979 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:19.979192Z"}
2025-05-26 13:18:20,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 13:18:20,571 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:20.571280Z"}
2025-05-26 13:18:20,588 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.587964Z"}
2025-05-26 13:18:20,618 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.618746Z"}
2025-05-26 13:18:20,636 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.636050Z"}
2025-05-26 13:18:20,754 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.754314Z"}
2025-05-26 13:18:20,758 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.758562Z"}
2025-05-26 13:18:20,770 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.770289Z"}
2025-05-26 13:18:20,774 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.773929Z"}
2025-05-26 13:18:20,784 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:20.782803Z"}
2025-05-26 13:18:20,839 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T17:18:20.839628Z"}
2025-05-26 13:18:21,095 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T17:18:21.095475Z"}
2025-05-26 13:18:21,119 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.118953Z"}
2025-05-26 13:18:21,123 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.123122Z"}
2025-05-26 13:18:21,128 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.128379Z"}
2025-05-26 13:18:21,131 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.131690Z"}
2025-05-26 13:18:21,137 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T17:18:21.136914Z"}
2025-05-26 13:18:21,144 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T17:18:21.144701Z"}
2025-05-26 13:18:21,147 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.147651Z"}
2025-05-26 13:18:21,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 13:18:21,353 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 13:18:21,362 - telegram.ext.Application - INFO - Application started
2025-05-26 13:18:21,368 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.368061Z"}
2025-05-26 13:18:21,390 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:21.390763Z"}
2025-05-26 13:18:21,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 13:18:21,459 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T17:18:21.459115Z"}
2025-05-26 13:18:21,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:22,296 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 13:18:23,600 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9vKgXDW6... sold 0.9901 SOL of $5t", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:23.600727Z"}
2025-05-26 13:18:25,915 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: VJBkhSqY... sold 0.0134 SOL of MOKO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:25.915403Z"}
2025-05-26 13:18:26,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Rnx8r45... sold 0.1000 SOL of FNQWeqnj", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:26.702398Z"}
2025-05-26 13:18:27,889 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7R8dwmAy... sold 2.9703 SOL of MAYA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:27.889666Z"}
2025-05-26 13:18:28,471 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8uWXcvQ3... sold 2.2300 SOL of maya", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:28.471591Z"}
2025-05-26 13:18:29,157 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of BITAPE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:29.157594Z"}
2025-05-26 13:18:31,873 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:33,739 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gvdn5Znu... sold 0.4950 SOL of RIP K9 Ban", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:33.739358Z"}
2025-05-26 13:18:37,155 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 93dggmM2... sold 0.2475 SOL of plastic", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:37.155621Z"}
2025-05-26 13:18:40,221 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9MygYi1g... sold 1.8000 SOL of COSWA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:40.221293Z"}
2025-05-26 13:18:41,971 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:43,905 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G9RzfqDx... sold 0.9901 SOL of TLC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:43.905545Z"}
2025-05-26 13:18:44,826 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2WUKgkjj... sold 2.3762 SOL of BLUE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:44.826399Z"}
2025-05-26 13:18:45,396 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3TNjowNo... sold 9.9010 SOL of Shimejis", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:45.395862Z"}
2025-05-26 13:18:45,831 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9ASL6KCv... sold 0.9901 SOL of flynak", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:45.830976Z"}
2025-05-26 13:18:50,798 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HUzj85RM... sold 0.0099 SOL of RMOON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:50.798358Z"}
2025-05-26 13:18:51,899 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DU3mYzxY... sold 0.0990 SOL of S&PUSS 500", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:51.899101Z"}
2025-05-26 13:18:52,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:18:58,573 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 46ozLRrH... sold 0.0000 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:58.573901Z"}
2025-05-26 13:18:58,720 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6oXmzx2i... sold 2.9703 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:58.720563Z"}
2025-05-26 13:18:59,313 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 69NkDB88... sold 0.0000 SOL of DOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:18:59.313194Z"}
2025-05-26 13:19:01,574 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6zt5ZWwH... sold 1.9802 SOL of USDC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:01.573906Z"}
2025-05-26 13:19:01,625 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:01.625583Z"}
2025-05-26 13:19:02,041 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ESQusYET... sold 1.4851 SOL of BashAI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.040968Z"}
2025-05-26 13:19:02,166 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:02,473 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DoqM97do... sold 0.9901 SOL of DogLovers", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.472970Z"}
2025-05-26 13:19:02,519 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: sozd8xJe... sold 1.1200 SOL of FrenchSlap", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:02.519597Z"}
2025-05-26 13:19:03,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2kRnzDco... sold 2.0000 SOL of BANDIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:03.241298Z"}
2025-05-26 13:19:05,157 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 1kKeYdcS... sold 1.2871 SOL of \u2205", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:05.157419Z"}
2025-05-26 13:19:05,315 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZt2R8ha... sold 2.1782 SOL of X PACKAGE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:05.315633Z"}
2025-05-26 13:19:10,638 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: APVEy4vU... sold 1.4851 SOL of FENT COIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:10.638751Z"}
2025-05-26 13:19:10,729 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DyxNedSJ... sold 0.9901 SOL of avinel", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:10.729105Z"}
2025-05-26 13:19:12,270 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:12,279 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8zgYte89... sold 0.0178 SOL of FLOCK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:12.279624Z"}
2025-05-26 13:19:12,362 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CnZ8TRVU... sold 1.1881 SOL of GMF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:12.362685Z"}
2025-05-26 13:19:13,724 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 622SJuVK... sold 1.2871 SOL of WALKCOIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:13.724101Z"}
2025-05-26 13:19:16,040 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9nM5fhrH... sold 61.3861 SOL of PEPEGROK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:16.040118Z"}
2025-05-26 13:19:22,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:31,838 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EoTWBXEo... sold 1.3861 SOL of bro", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:31.836963Z"}
2025-05-26 13:19:32,630 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:36,433 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G84Yz6fD... sold 1.9802 SOL of Otter", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:36.433491Z"}
2025-05-26 13:19:39,824 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CEUA7zVo... sold 1.6832 SOL of RIPCHARLIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:39.824818Z"}
2025-05-26 13:19:41,349 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ANQvetBM... sold 1.9802 SOL of SOLDEGENS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:41.348938Z"}
2025-05-26 13:19:42,270 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9Dzw5wF9... sold 0.0001 SOL of 0.0.0.0.0.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:42.269772Z"}
2025-05-26 13:19:42,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:44,112 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of TAYLOR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:44.112720Z"}
2025-05-26 13:19:44,420 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CeU2b1KW... sold 0.1980 SOL of OLDGECKO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:44.420331Z"}
2025-05-26 13:19:45,022 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:45.022442Z"}
2025-05-26 13:19:53,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:19:53,032 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmgKiEqo... sold 0.4950 SOL of SpaceX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.032661Z"}
2025-05-26 13:19:53,704 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2DGWmnQq... sold 0.9901 SOL of POT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.704425Z"}
2025-05-26 13:19:53,928 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of Jan44734", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:53.928109Z"}
2025-05-26 13:19:55,115 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9PXd1QDB... sold 2.9703 SOL of FART", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:55.115488Z"}
2025-05-26 13:19:56,948 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G3xCvMCH... sold 2.7525 SOL of FOUNDRY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:19:56.948580Z"}
2025-05-26 13:20:02,237 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CXUw3Jp2... sold 1.6832 SOL of avcore", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:02.237276Z"}
2025-05-26 13:20:03,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:03,331 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5aD1fNHZ... sold 3.0000 SOL of PIZZI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:03.331347Z"}
2025-05-26 13:20:05,058 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Hnv9TkQd... sold 1.9000 SOL of Oyajichi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:05.058070Z"}
2025-05-26 13:20:05,338 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of FAGWHEEL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:05.338293Z"}
2025-05-26 13:20:08,381 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3PC5geEF... sold 1.9802 SOL of Don Tusk", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:08.381570Z"}
2025-05-26 13:20:12,989 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CS2rHpJj... sold 5.0000 SOL of Roadster", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:12.989807Z"}
2025-05-26 13:20:13,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:13,917 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9vKgXDW6... sold 0.9901 SOL of \ud83e\udd23", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:13.916095Z"}
2025-05-26 13:20:17,442 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVczPwnN... sold 2.9703 SOL of GOONDAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:17.442884Z"}
2025-05-26 13:20:17,737 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RM4b8kT... sold 0.0020 SOL of Bob", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:17.737904Z"}
2025-05-26 13:20:18,655 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DU3mYzxY... sold 0.0990 SOL of S&PUSSY 69", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:18.655894Z"}
2025-05-26 13:20:19,335 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of Toberichal", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:19.334932Z"}
2025-05-26 13:20:20,670 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Ei4X8Lyc... sold 5.9406 SOL of h1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:20.670453Z"}
2025-05-26 13:20:20,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 0.9901 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:20.702190Z"}
2025-05-26 13:20:22,563 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of 0xblastard", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:22.563080Z"}
2025-05-26 13:20:23,394 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:25,584 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: WRXFrF1p... sold 0.9901 SOL of NIC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:25.584197Z"}
2025-05-26 13:20:25,655 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3hwS51f1... sold 1.9802 SOL of firecoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:25.655844Z"}
2025-05-26 13:20:29,694 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gvdn5Znu... sold 0.9901 SOL of RIP K9 Ban", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:29.694222Z"}
2025-05-26 13:20:29,764 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GewBprxs... sold 5.0000 SOL of Roadster", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:29.764285Z"}
2025-05-26 13:20:33,501 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:36,950 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5ZeU9Lbc... sold 3.0000 SOL of JAMES", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:36.950154Z"}
2025-05-26 13:20:41,608 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVczPwnN... sold 2.9703 SOL of GOONDAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:41.608504Z"}
2025-05-26 13:20:43,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:47,396 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 93dggmM2... sold 0.2475 SOL of FAGWHEEL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:47.396155Z"}
2025-05-26 13:20:51,082 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BjCcu5RH... sold 0.4987 SOL of WW3MUPPETS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:51.081998Z"}
2025-05-26 13:20:52,617 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9MusJmn6... sold 0.3960 SOL of Maya", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:52.616986Z"}
2025-05-26 13:20:53,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:20:57,730 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FBo4KX47... sold 0.5941 SOL of Argt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:20:57.730237Z"}
2025-05-26 13:21:02,380 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5td6X6S4... sold 4.9505 SOL of DONTBUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:02.380411Z"}
2025-05-26 13:21:02,540 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of P33STUDiO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:02.540092Z"}
2025-05-26 13:21:03,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:09,198 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: JE754JqB... sold 0.1000 SOL of BRASIZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:09.198284Z"}
2025-05-26 13:21:10,435 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dp1XWk5a... sold 1.1990 SOL of who", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:10.434832Z"}
2025-05-26 13:21:11,050 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ZUkJdSLE... sold 2.0000 SOL of \ud83e\udd90", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:11.050678Z"}
2025-05-26 13:21:14,086 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:15,048 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of bike", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:15.047907Z"}
2025-05-26 13:21:16,585 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4b1Xr46u... sold 0.1980 SOL of prison", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:16.585636Z"}
2025-05-26 13:21:24,177 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:25,179 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of DRAGAPEX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:25.179674Z"}
2025-05-26 13:21:28,559 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ANQvetBM... sold 1.9802 SOL of MAGNUS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:28.558968Z"}
2025-05-26 13:21:32,339 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F4dmmtM7... sold 0.4257 SOL of BEACH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:32.339016Z"}
2025-05-26 13:21:32,477 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: JE754JqB... sold 0.1000 SOL of BRASIZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:32.477714Z"}
2025-05-26 13:21:34,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:35,931 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fy3XcLYZ... sold 0.2970 SOL of \ud83d\ude80", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:35.931710Z"}
2025-05-26 13:21:36,239 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8rSeJJRZ... sold 0.0099 SOL of TESTF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:36.239480Z"}
2025-05-26 13:21:37,467 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DKuNKQed... sold 1.6337 SOL of CWB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:37.467402Z"}
2025-05-26 13:21:40,539 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: oNJ2nas9... sold 1.4851 SOL of hyperchill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:40.539554Z"}
2025-05-26 13:21:44,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:51,102 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZt2R8ha... sold 1.5446 SOL of ROSS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:51.102368Z"}
2025-05-26 13:21:52,214 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AqVnksyB... sold 0.9901 SOL of ELON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.214535Z"}
2025-05-26 13:21:52,257 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: B2CRi1hs... sold 0.1000 SOL of MAYA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.257713Z"}
2025-05-26 13:21:52,307 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 667AWcos... sold 2.9703 SOL of LUPIA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:52.307299Z"}
2025-05-26 13:21:54,454 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:21:56,019 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EoWwfq9N... sold 0.0010 SOL of botcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:56.019824Z"}
2025-05-26 13:21:57,435 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9sCcAxe5... sold 4.0000 SOL of WT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:57.435894Z"}
2025-05-26 13:21:59,279 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Hw5w1axG... sold 0.0593 SOL of SETH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:21:59.279147Z"}
2025-05-26 13:22:03,272 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: APVEy4vU... sold 1.4851 SOL of STD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:03.272506Z"}
2025-05-26 13:22:04,548 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:09,330 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5uvXxXjL... sold 1.9000 SOL of BarbieGirl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:09.329117Z"}
2025-05-26 13:22:09,941 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EwyvPTKi... sold 1.1000 SOL of CryptoSwar", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:09.941377Z"}
2025-05-26 13:22:13,102 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of OggyStealS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:13.102257Z"}
2025-05-26 13:22:13,153 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7i6bfTP8... sold 1.4900 SOL of Cubikka", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:13.152943Z"}
2025-05-26 13:22:14,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:15,270 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of CRYPT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:15.270871Z"}
2025-05-26 13:22:15,994 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CrMSFMdW... sold 1.1881 SOL of TRRLO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:15.994479Z"}
2025-05-26 13:22:19,339 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DSVNmMZi... sold 0.9901 SOL of STARSHIP 3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:19.339578Z"}
2025-05-26 13:22:19,365 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 44nFNkKU... sold 0.9901 SOL of pgooner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:19.365412Z"}
2025-05-26 13:22:20,965 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AAPkNToN... sold 0.2077 SOL of moondog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:20.965896Z"}
2025-05-26 13:22:21,432 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2xKDETTh... sold 2.9703 SOL of RIP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:21.432453Z"}
2025-05-26 13:22:21,507 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FjeaKDFA... sold 2.9703 SOL of Mr Bean", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:21.507706Z"}
2025-05-26 13:22:24,966 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:25,579 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9maDquBn... sold 0.0297 SOL of LockN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:25.579458Z"}
2025-05-26 13:22:26,500 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: vGwtpGFM... sold 0.0010 SOL of CSR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:26.500638Z"}
2025-05-26 13:22:29,504 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 66666KoH... sold 1.9802 SOL of Mr Bean", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:29.504817Z"}
2025-05-26 13:22:34,341 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HM11w5fe... sold 0.1188 SOL of squid", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:34.341679Z"}
2025-05-26 13:22:35,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:36,022 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9r8NuiwA... sold 3.8565 SOL of SUPR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:36.022516Z"}
2025-05-26 13:22:36,944 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2cJN9h83... sold 1.6000 SOL of ButtHurt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:36.943918Z"}
2025-05-26 13:22:38,508 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GGbQBvbq... sold 0.8911 SOL of LR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:38.508559Z"}
2025-05-26 13:22:43,239 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BoLTdQey... sold 0.9802 SOL of 59s", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:43.239162Z"}
2025-05-26 13:22:43,295 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2Zobi8ji... sold 0.1980 SOL of fly", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:43.295651Z"}
2025-05-26 13:22:45,076 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of IA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:45.075955Z"}
2025-05-26 13:22:45,148 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:45,430 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of TRUMPACCTS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:45.430330Z"}
2025-05-26 13:22:54,453 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVWP2wwf... sold 3.0000 SOL of send", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:54.452980Z"}
2025-05-26 13:22:55,202 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 723CcrTR... sold 0.7733 SOL of MOG16", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:55.202795Z"}
2025-05-26 13:22:55,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 13:22:57,821 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AV4zBZSe... sold 1.5842 SOL of MR BEAN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:57.821671Z"}
2025-05-26 13:22:59,060 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FaPWKVMi... sold 1.0891 SOL of PEPLUSH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:22:59.060775Z"}
2025-05-26 13:23:03,593 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8R7Do5RU... sold 0.0079 SOL of BTC BOOM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:23:03.593473Z"}
2025-05-26 13:23:04,460 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EtV6GqY9... sold 1.4851 SOL of TIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T17:23:04.460403Z"}
2025-05-26 18:25:46,310 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:46.310068Z"}
2025-05-26 18:25:46,658 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T22:25:46.658894Z"}
2025-05-26 18:25:46,734 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:46.733956Z"}
2025-05-26 18:25:46,744 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:46.744393Z"}
2025-05-26 18:25:46,751 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T22:25:46.751155Z"}
2025-05-26 18:25:46,755 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:25:46.755391Z"}
2025-05-26 18:25:47,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 18:25:47,410 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:25:47.410006Z"}
2025-05-26 18:25:47,416 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.416797Z"}
2025-05-26 18:25:47,426 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.426166Z"}
2025-05-26 18:25:47,477 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.477329Z"}
2025-05-26 18:25:47,509 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.509431Z"}
2025-05-26 18:25:47,512 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.512572Z"}
2025-05-26 18:25:47,529 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.528945Z"}
2025-05-26 18:25:47,555 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.554792Z"}
2025-05-26 18:25:47,577 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:47.577598Z"}
2025-05-26 18:25:47,678 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T22:25:47.678112Z"}
2025-05-26 18:25:47,690 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T22:25:47.689858Z"}
2025-05-26 18:25:47,726 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:47.726101Z"}
2025-05-26 18:25:49,575 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:49.575734Z"}
2025-05-26 18:25:49,609 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:49.596621Z"}
2025-05-26 18:25:49,695 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:49.695365Z"}
2025-05-26 18:25:49,711 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:25:49.711708Z"}
2025-05-26 18:25:49,757 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T22:25:49.757630Z"}
2025-05-26 18:25:49,767 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:49.767085Z"}
2025-05-26 18:25:50,012 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 18:25:50,025 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 18:25:50,028 - telegram.ext.Application - INFO - Application started
2025-05-26 18:25:50,051 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:50.051145Z"}
2025-05-26 18:25:50,076 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:50.075216Z"}
2025-05-26 18:25:50,146 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 18:25:50,157 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:25:50.157451Z"}
2025-05-26 18:25:50,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:25:50,706 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 400 Bad Request"
2025-05-26 18:25:50,710 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 360, in button_callback
    await query.answer()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 180, in answer
    return await self.get_bot().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 778, in answer_callback_query
    return await super().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3213, in answer_callback_query
    return await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-26 18:25:50,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 400 Bad Request"
2025-05-26 18:25:51,028 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 360, in button_callback
    await query.answer()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 180, in answer
    return await self.get_bot().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 778, in answer_callback_query
    return await super().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3213, in answer_callback_query
    return await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-26 18:25:54,716 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4LVFuib4... sold 1.9802 SOL of PEPECASH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:54.716642Z"}
2025-05-26 18:25:56,497 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6ruvDYrD... sold 1.9802 SOL of IPO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:56.497021Z"}
2025-05-26 18:25:57,926 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G3mVExWm... sold 0.4950 SOL of Hodl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:57.925859Z"}
2025-05-26 18:25:59,595 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5ry6xipR... sold 1.6864 SOL of TODA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:59.595369Z"}
2025-05-26 18:25:59,768 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5ocuGGEC... sold 1.4663 SOL of TRENCHIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:25:59.767949Z"}
2025-05-26 18:26:00,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:04,457 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4vejd1ry... sold 1.4851 SOL of FULLPORT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:04.457761Z"}
2025-05-26 18:26:06,835 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GpijMUm... sold 0.9901 SOL of grenboi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:06.835296Z"}
2025-05-26 18:26:06,953 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AfKqD4nZ... sold 1.6864 SOL of TODA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:06.952997Z"}
2025-05-26 18:26:10,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:12,667 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3NMn55Rx... sold 1.8812 SOL of shit", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:12.667439Z"}
2025-05-26 18:26:13,588 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 74LgHx4z... sold 2.9703 SOL of DV3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:13.588804Z"}
2025-05-26 18:26:14,719 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 49nSpmxw... sold 1.0891 SOL of Tanner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:14.719435Z"}
2025-05-26 18:26:18,723 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gt5t3J1x... sold 1.4851 SOL of COW", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:18.723126Z"}
2025-05-26 18:26:18,775 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: MFznJg5c... sold 1.9957 SOL of Tanner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:18.775436Z"}
2025-05-26 18:26:18,939 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EMeKZkQJ... sold 1.0000 SOL of MPW", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:18.939025Z"}
2025-05-26 18:26:19,737 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GxjBNaQc... sold 0.9901 SOL of INFLUENCE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:19.737723Z"}
2025-05-26 18:26:19,808 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6UwHzKQP... sold 1.0000 SOL of CryptoCons", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:19.808810Z"}
2025-05-26 18:26:20,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:25,145 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Db1g8u2n... sold 7.9208 SOL of GMI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:25.145258Z"}
2025-05-26 18:26:27,879 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AD1mB49Q... sold 0.0500 SOL of RND1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:27.879451Z"}
2025-05-26 18:26:31,059 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:32,322 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of ANONYM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:32.322814Z"}
2025-05-26 18:26:33,201 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of NOICE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:33.201771Z"}
2025-05-26 18:26:33,676 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FBdKJMmM... sold 2.9703 SOL of SKILL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:33.676559Z"}
2025-05-26 18:26:36,627 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 65mCJfTf... sold 2.0000 SOL of $NAI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:36.626997Z"}
2025-05-26 18:26:38,028 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6dqfFhtF... sold 1.2000 SOL of BRAIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:38.028483Z"}
2025-05-26 18:26:41,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:48,915 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6diAtT1i... sold 3.0000 SOL of Censored", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:48.915205Z"}
2025-05-26 18:26:49,139 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8PR9gJow... sold 0.7921 SOL of orangie", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:49.138980Z"}
2025-05-26 18:26:49,836 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4LVFuib4... sold 1.9802 SOL of TRUMPCOIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:49.836510Z"}
2025-05-26 18:26:49,966 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BLm4RRwc... sold 2.0000 SOL of PVP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:49.966175Z"}
2025-05-26 18:26:51,371 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:26:52,603 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HFPa983C... sold 0.4950 SOL of CROW", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:52.602809Z"}
2025-05-26 18:26:53,796 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EWPefkYv... sold 0.9901 SOL of OneCoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:53.796047Z"}
2025-05-26 18:26:57,830 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of MANIFEST", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:57.827682Z"}
2025-05-26 18:26:57,971 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9bVND3C8... sold 0.9901 SOL of Lambobu", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:57.971099Z"}
2025-05-26 18:26:58,452 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4eG3pbV6... sold 0.0297 SOL of NEWPORT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:26:58.452508Z"}
2025-05-26 18:27:01,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:02,188 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: A8KaH4NE... sold 1.2871 SOL of LORE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:02.188774Z"}
2025-05-26 18:27:03,966 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7NDHsLbz... sold 0.0000 SOL of SelamiSeyi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:03.966840Z"}
2025-05-26 18:27:04,242 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4evdt6SS... sold 3.0000 SOL of MANTIS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:04.241955Z"}
2025-05-26 18:27:05,321 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GpijMUm... sold 0.9901 SOL of brok", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:05.312734Z"}
2025-05-26 18:27:06,127 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AsMwZDHv... sold 1.0000 SOL of KANSEI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:06.127040Z"}
2025-05-26 18:27:08,575 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:09,082 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:27:10,129 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9TNtkdDK... sold 5.0000 SOL of MONGO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:10.129408Z"}
2025-05-26 18:27:11,645 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HKQ2m2Z2... sold 1.9802 SOL of bratz", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:11.645085Z"}
2025-05-26 18:27:14,309 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: YKsdAJqh... sold 2.8500 SOL of KAWS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:14.309692Z"}
2025-05-26 18:27:17,059 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CU6NSebF... sold 0.9901 SOL of RlGETTl", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:17.059547Z"}
2025-05-26 18:27:18,730 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:18,818 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Ef2TCv2k... sold 1.9000 SOL of Tanner", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:18.818390Z"}
2025-05-26 18:27:19,631 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of TOPBLAST", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:19.631728Z"}
2025-05-26 18:27:21,717 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4mR8nMmb... sold 1.9802 SOL of BitDog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:21.717306Z"}
2025-05-26 18:27:25,324 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DVmcDrci... sold 3.0000 SOL of MONGO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:25.324764Z"}
2025-05-26 18:27:25,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:25,925 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 18:27:26,696 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9RWzqUzs... sold 1.9802 SOL of gotcha", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:26.696061Z"}
2025-05-26 18:27:29,325 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3Tx9rimP... sold 1.9802 SOL of DarkCoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:29.325111Z"}
2025-05-26 18:27:31,418 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3K8HCjxV... sold 0.9901 SOL of VEIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:31.418285Z"}
2025-05-26 18:27:32,816 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BS5bp77G... sold 1.2871 SOL of SDERIV", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:32.811589Z"}
2025-05-26 18:27:35,124 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6YFaXy5a... sold 0.2970 SOL of NVIDIA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:35.124015Z"}
2025-05-26 18:27:35,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:37,107 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CM3KxcCz... sold 1.1881 SOL of Loveski", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:37.107864Z"}
2025-05-26 18:27:37,763 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CSL9Cab3... sold 1.9957 SOL of bratz", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:37.763266Z"}
2025-05-26 18:27:39,290 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3Rb64K2v... sold 4.9307 SOL of RDOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:39.290167Z"}
2025-05-26 18:27:45,082 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HpmiTLon... sold 2.0000 SOL of OWKEE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:45.082014Z"}
2025-05-26 18:27:45,129 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: XDpDXxD5... sold 1.9802 SOL of Hand", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:45.129039Z"}
2025-05-26 18:27:45,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:48,516 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of PHYSICIST", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:48.516819Z"}
2025-05-26 18:27:51,751 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ATRn826L... sold 0.9900 SOL of PANCAKE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:51.751120Z"}
2025-05-26 18:27:53,520 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fxgd9QHS... sold 0.9901 SOL of trollcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:53.520077Z"}
2025-05-26 18:27:53,666 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: D4V41jWV... sold 0.0297 SOL of KERMIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:53.666626Z"}
2025-05-26 18:27:55,829 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:27:55,897 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2hTeVuXA... sold 0.0990 SOL of BUBUTARD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:55.897226Z"}
2025-05-26 18:27:55,989 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Dj1cUYo... sold 1.0000 SOL of orange", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:55.989178Z"}
2025-05-26 18:27:56,560 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BLm4RRwc... sold 2.0000 SOL of mememart", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:27:56.559810Z"}
2025-05-26 18:28:01,408 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BoGxGZ5y... sold 1.9986 SOL of MHGA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:01.408825Z"}
2025-05-26 18:28:03,250 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EJyP6A4t... sold 1.9802 SOL of Lightning", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:03.250677Z"}
2025-05-26 18:28:03,662 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZ85GFTe... sold 1.4940 SOL of Pooper", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:03.661895Z"}
2025-05-26 18:28:06,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:07,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:08,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:28:08,493 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: uDv4YUKA... sold 1.7822 SOL of TIME", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:08.493376Z"}
2025-05-26 18:28:09,393 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3GfP47xW... sold 5.0000 SOL of LOLA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:09.393622Z"}
2025-05-26 18:28:09,466 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 36DWP52M... sold 8.9000 SOL of lola", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:09.466288Z"}
2025-05-26 18:28:10,463 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8cWkmK6v... sold 0.9901 SOL of Librium", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:10.463567Z"}
2025-05-26 18:28:10,521 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F1kPoBCx... sold 0.9900 SOL of DEER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:10.521379Z"}
2025-05-26 18:28:10,927 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 83QQFLxc... sold 13.0000 SOL of lola", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:10.927852Z"}
2025-05-26 18:28:11,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:11,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 18:28:11,597 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/editMessageText "HTTP/1.1 200 OK"
2025-05-26 18:28:14,150 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EBbQ8XAa... sold 3.0000 SOL of LOLA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:14.149982Z"}
2025-05-26 18:28:14,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:14,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:28:20,926 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3r9itQab... sold 0.9901 SOL of HOLD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:20.926734Z"}
2025-05-26 18:28:21,373 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 35vxt3dg... sold 1.2376 SOL of SEAHAVEN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:21.373735Z"}
2025-05-26 18:28:24,444 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:26,161 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 1.4851 SOL of 1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:26.161684Z"}
2025-05-26 18:28:30,246 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9W9JMJ71... sold 0.9594 SOL of DEBUBU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:30.246532Z"}
2025-05-26 18:28:31,818 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F64s8Agh... sold 0.9901 SOL of bsp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:31.818239Z"}
2025-05-26 18:28:34,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:39,803 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EuD93tAE... sold 1.4663 SOL of BAGIFY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:39.803314Z"}
2025-05-26 18:28:41,142 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DvM4WgfY... sold 1.1881 SOL of DaddyFrog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:41.141944Z"}
2025-05-26 18:28:41,378 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EMciuCx4... sold 2.5743 SOL of airedex", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:41.378617Z"}
2025-05-26 18:28:43,684 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of LOLA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:43.684504Z"}
2025-05-26 18:28:44,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:48,397 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GpijMUm... sold 0.9901 SOL of PC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:48.396887Z"}
2025-05-26 18:28:51,946 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: mtaDFhB9... sold 0.6931 SOL of MONSTER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:51.946688Z"}
2025-05-26 18:28:53,934 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3J9fPWHy... sold 0.1287 SOL of 9TO5", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:53.934136Z"}
2025-05-26 18:28:54,810 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:28:57,005 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmNMqM5V... sold 1.9802 SOL of Dandy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:57.005020Z"}
2025-05-26 18:28:57,926 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4evdt6SS... sold 3.0000 SOL of MANTIS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:28:57.926562Z"}
2025-05-26 18:29:02,156 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2TTzNPtH... sold 0.1980 SOL of protogen", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:29:02.156280Z"}
2025-05-26 18:29:02,539 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AzbKWG1b... sold 0.0495 SOL of Skeetcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:29:02.539823Z"}
2025-05-26 18:31:42,632 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:42.632312Z"}
2025-05-26 18:31:42,836 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T22:31:42.835666Z"}
2025-05-26 18:31:42,844 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:42.843978Z"}
2025-05-26 18:31:42,867 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:42.864840Z"}
2025-05-26 18:31:42,872 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T22:31:42.871567Z"}
2025-05-26 18:31:42,878 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:31:42.878709Z"}
2025-05-26 18:31:43,537 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 18:31:43,546 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:31:43.546145Z"}
2025-05-26 18:31:43,548 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.548441Z"}
2025-05-26 18:31:43,567 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.567790Z"}
2025-05-26 18:31:43,610 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.610109Z"}
2025-05-26 18:31:43,646 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.646057Z"}
2025-05-26 18:31:43,734 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.733963Z"}
2025-05-26 18:31:43,773 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.773145Z"}
2025-05-26 18:31:43,801 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.800062Z"}
2025-05-26 18:31:43,809 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.808804Z"}
2025-05-26 18:31:43,861 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T22:31:43.861149Z"}
2025-05-26 18:31:43,878 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T22:31:43.878226Z"}
2025-05-26 18:31:43,932 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:43.932167Z"}
2025-05-26 18:31:43,958 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.957576Z"}
2025-05-26 18:31:43,996 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:43.995932Z"}
2025-05-26 18:31:44,003 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:44.003788Z"}
2025-05-26 18:31:44,014 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:31:44.014465Z"}
2025-05-26 18:31:44,023 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T22:31:44.023479Z"}
2025-05-26 18:31:44,031 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:44.030807Z"}
2025-05-26 18:31:44,207 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:44.206964Z"}
2025-05-26 18:31:44,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 18:31:44,319 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 18:31:44,339 - telegram.ext.Application - INFO - Application started
2025-05-26 18:31:44,364 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:44.364714Z"}
2025-05-26 18:31:44,370 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fys5tije... sold 2.9703 SOL of 3e4r4tghy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:44.370456Z"}
2025-05-26 18:31:44,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 18:31:44,577 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:31:44.576255Z"}
2025-05-26 18:31:44,938 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:31:45,253 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:31:45,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:31:45,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 400 Bad Request"
2025-05-26 18:31:45,779 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 360, in button_callback
    await query.answer()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 180, in answer
    return await self.get_bot().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 778, in answer_callback_query
    return await super().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3213, in answer_callback_query
    return await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-26 18:31:46,079 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3GfP47xW... sold 5.0000 SOL of MACA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:46.079304Z"}
2025-05-26 18:31:46,130 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 400 Bad Request"
2025-05-26 18:31:46,146 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 360, in button_callback
    await query.answer()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 180, in answer
    return await self.get_bot().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 778, in answer_callback_query
    return await super().answer_callback_query(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3213, in answer_callback_query
    return await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Query is too old and response timeout expired or query id is invalid
2025-05-26 18:31:48,710 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6EgND3B8... sold 0.9600 SOL of KF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:48.710375Z"}
2025-05-26 18:31:52,708 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6RmPfs3k... sold 2.0000 SOL of KF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:52.708518Z"}
2025-05-26 18:31:55,124 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:31:56,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DD7BGECk... sold 0.2574 SOL of 100mil", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:56.702041Z"}
2025-05-26 18:31:58,237 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2vYTNjhP... sold 4.8000 SOL of MACA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:58.237295Z"}
2025-05-26 18:31:59,098 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: C2gw4Vza... sold 1.6000 SOL of CALC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:59.098640Z"}
2025-05-26 18:31:59,473 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F1M3fncB... sold 0.9700 SOL of Bink", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:31:59.472986Z"}
2025-05-26 18:32:04,178 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5r7c2MHp... sold 1.9802 SOL of LUCKY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:04.178321Z"}
2025-05-26 18:32:04,679 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DBHhBFV6... sold 0.9901 SOL of Giraffe", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:04.679157Z"}
2025-05-26 18:32:05,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:08,285 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of BIGBANG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:08.285604Z"}
2025-05-26 18:32:08,379 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FHkvAeEv... sold 2.0000 SOL of GUWEIZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:08.379297Z"}
2025-05-26 18:32:10,218 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5r1HzfMn... sold 1.9802 SOL of frok", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:10.218231Z"}
2025-05-26 18:32:13,466 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3GfP47xW... sold 5.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:13.466292Z"}
2025-05-26 18:32:13,494 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmNMqM5V... sold 1.9802 SOL of Greeny", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:13.494359Z"}
2025-05-26 18:32:14,454 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: cartidEt... sold 15.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:14.454295Z"}
2025-05-26 18:32:14,664 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7GaE86n5... sold 13.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:14.664347Z"}
2025-05-26 18:32:15,048 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BwaVFCDJ... sold 10.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:15.048286Z"}
2025-05-26 18:32:15,440 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:15,444 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: dev6SDTV... sold 0.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:15.444256Z"}
2025-05-26 18:32:15,660 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7GhWwhaM... sold 15.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:15.660043Z"}
2025-05-26 18:32:15,857 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: iSbXfAXf... sold 1.4920 SOL of caaaaaaaat", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:15.856969Z"}
2025-05-26 18:32:16,845 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BoGxGZ5y... sold 1.9986 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:16.844977Z"}
2025-05-26 18:32:17,093 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 36DWP52M... sold 8.9000 SOL of rip sammy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:17.092955Z"}
2025-05-26 18:32:17,672 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2vYTNjhP... sold 4.8000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:17.672324Z"}
2025-05-26 18:32:20,354 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Efd2Zc5m... sold 3.0115 SOL of LTC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:20.354846Z"}
2025-05-26 18:32:20,407 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DuWQpuU3... sold 1.9000 SOL of SELL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:20.407213Z"}
2025-05-26 18:32:20,445 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9LFLuhNa... sold 1.6864 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:20.445348Z"}
2025-05-26 18:32:21,278 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7GaE86n5... sold 13.0000 SOL of RS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:21.278218Z"}
2025-05-26 18:32:21,334 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2dyDktKn... sold 1.4000 SOL of Chain|Mag", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:21.333820Z"}
2025-05-26 18:32:25,471 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: En5AaBtu... sold 1.9802 SOL of Lovot", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:25.471120Z"}
2025-05-26 18:32:25,599 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:26,499 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FrJoNhzV... sold 2.0000 SOL of king DuKE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:26.499751Z"}
2025-05-26 18:32:27,115 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: dev6SDTV... sold 0.0000 SOL of RS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:27.115602Z"}
2025-05-26 18:32:28,266 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3mtADs86... sold 0.9337 SOL of wigi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:28.265926Z"}
2025-05-26 18:32:28,368 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:28,399 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DBg4BnH6... sold 1.2871 SOL of MurG-AI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:28.399029Z"}
2025-05-26 18:32:28,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:32:30,797 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GVczPwnN... sold 2.9703 SOL of Samuel", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:30.797557Z"}
2025-05-26 18:32:31,707 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CcVhzDkr... sold 1.9000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:31.707209Z"}
2025-05-26 18:32:32,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:32,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 18:32:33,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/editMessageText "HTTP/1.1 200 OK"
2025-05-26 18:32:33,610 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: dev6SDTV... sold 0.0000 SOL of RIPSAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:33.610637Z"}
2025-05-26 18:32:34,075 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5xFdEbnG... sold 0.9000 SOL of Uprising", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:34.075614Z"}
2025-05-26 18:32:37,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GFA2t9fZ... sold 2.9703 SOL of CASECLOSED", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:37.240999Z"}
2025-05-26 18:32:40,102 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EzeGwS6W... sold 0.9495 SOL of Drill", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:40.102333Z"}
2025-05-26 18:32:42,855 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:43,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:43,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 18:32:44,590 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G7NvZKjo... sold 1.9802 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:44.590591Z"}
2025-05-26 18:32:44,734 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3mQvUx8J... sold 1.6864 SOL of RS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:44.734489Z"}
2025-05-26 18:32:44,817 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2nyUSzHR... sold 3.0000 SOL of pepzard", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:44.817099Z"}
2025-05-26 18:32:48,701 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FZt2R8ha... sold 1.9802 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:48.701071Z"}
2025-05-26 18:32:48,815 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HY4Sek2U... sold 0.0099 SOL of BUY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:48.813582Z"}
2025-05-26 18:32:49,425 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9TNtkdDK... sold 5.0000 SOL of KF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:49.425400Z"}
2025-05-26 18:32:49,622 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GyiAMVLu... sold 1.9802 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:49.622910Z"}
2025-05-26 18:32:52,300 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DR7ghPiR... sold 2.4752 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:52.300247Z"}
2025-05-26 18:32:52,330 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of THREE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:52.329921Z"}
2025-05-26 18:32:53,234 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GxjBNaQc... sold 0.9901 SOL of Exposure", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:53.233455Z"}
2025-05-26 18:32:53,413 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:32:53,712 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: mtaDFhB9... sold 0.4950 SOL of MONSTER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:53.712125Z"}
2025-05-26 18:32:57,224 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5fMuWkZB... sold 1.9000 SOL of limitless", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:32:57.224392Z"}
2025-05-26 18:33:16,732 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:16.732194Z"}
2025-05-26 18:33:16,902 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-26T22:33:16.902552Z"}
2025-05-26 18:33:16,906 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:16.906095Z"}
2025-05-26 18:33:16,911 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:16.910976Z"}
2025-05-26 18:33:16,920 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-26T22:33:16.920733Z"}
2025-05-26 18:33:16,936 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:33:16.936846Z"}
2025-05-26 18:33:17,576 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 18:33:17,603 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:33:17.602373Z"}
2025-05-26 18:33:17,612 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.612035Z"}
2025-05-26 18:33:17,618 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.618006Z"}
2025-05-26 18:33:17,623 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.622726Z"}
2025-05-26 18:33:17,630 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.630712Z"}
2025-05-26 18:33:17,634 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.634721Z"}
2025-05-26 18:33:17,638 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.638310Z"}
2025-05-26 18:33:17,647 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.647413Z"}
2025-05-26 18:33:17,653 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.653057Z"}
2025-05-26 18:33:17,680 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-26T22:33:17.680592Z"}
2025-05-26 18:33:17,693 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-26T22:33:17.693209Z"}
2025-05-26 18:33:17,701 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:17.701086Z"}
2025-05-26 18:33:17,705 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.705421Z"}
2025-05-26 18:33:17,714 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.713983Z"}
2025-05-26 18:33:17,724 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.724371Z"}
2025-05-26 18:33:17,735 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-26T22:33:17.735218Z"}
2025-05-26 18:33:17,749 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-26T22:33:17.749423Z"}
2025-05-26 18:33:17,755 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:17.755605Z"}
2025-05-26 18:33:18,036 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:18.036022Z"}
2025-05-26 18:33:18,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 18:33:18,064 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 18:33:18,099 - telegram.ext.Application - INFO - Application started
2025-05-26 18:33:18,188 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:18.188242Z"}
2025-05-26 18:33:18,272 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 18:33:18,282 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-26T22:33:18.282505Z"}
2025-05-26 18:33:18,496 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EuD93tAE... sold 3.0832 SOL of BAGIFY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:18.496146Z"}
2025-05-26 18:33:21,549 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 74LgHx4z... sold 2.9703 SOL of 200PUMPED", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:21.549106Z"}
2025-05-26 18:33:25,163 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5vAsUjJn... sold 2.4752 SOL of csdpae", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:25.163018Z"}
2025-05-26 18:33:27,010 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3NMn55Rx... sold 1.7822 SOL of Bounce", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:27.010551Z"}
2025-05-26 18:33:27,707 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4o3yQZU6... sold 2.5743 SOL of Sammy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:27.707806Z"}
2025-05-26 18:33:27,868 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6HfntwEB... sold 1.9802 SOL of Conspiracy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:27.868368Z"}
2025-05-26 18:33:28,855 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:33:30,698 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7eiGgyRs... sold 1.9802 SOL of Benny ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:30.698164Z"}
2025-05-26 18:33:30,771 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of TICKER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:30.770973Z"}
2025-05-26 18:33:32,154 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9TNtkdDK... sold 5.0000 SOL of KF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:32.154180Z"}
2025-05-26 18:33:32,911 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ErNwtzH2... sold 2.9703 SOL of retardcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:32.911195Z"}
2025-05-26 18:33:33,319 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GGdxDu7y... sold 1.9345 SOL of DERP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:33.319403Z"}
2025-05-26 18:33:34,383 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GpijMUm... sold 0.9901 SOL of BOT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:34.383576Z"}
2025-05-26 18:33:39,008 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:33:39,141 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 71PA9zV8... sold 0.9901 SOL of dvces", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:39.141217Z"}
2025-05-26 18:33:39,605 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DR7ghPiR... sold 2.4752 SOL of RIP SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:39.605415Z"}
2025-05-26 18:33:43,905 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CJdc9VpW... sold 2.0000 SOL of SAMMY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:43.905092Z"}
2025-05-26 18:33:47,369 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5Mj2GV5Z... sold 1.9802 SOL of $Panda", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:47.369459Z"}
2025-05-26 18:33:48,413 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 56d6rLmS... sold 0.9901 SOL of RNC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:48.412977Z"}
2025-05-26 18:33:48,466 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AzVeJaeS... sold 0.0792 SOL of aaqa", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:48.466875Z"}
2025-05-26 18:33:49,216 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:33:49,600 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AmNMqM5V... sold 1.9802 SOL of ZUCK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:49.600161Z"}
2025-05-26 18:33:50,566 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6irpf6LR... sold 0.6634 SOL of selfmade", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:50.564598Z"}
2025-05-26 18:33:54,565 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 2.4752 SOL of yuge", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:54.565385Z"}
2025-05-26 18:33:58,089 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of BOT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:58.088990Z"}
2025-05-26 18:33:58,649 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Byonzktg... sold 0.0090 SOL of INFINITE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:58.648977Z"}
2025-05-26 18:33:58,815 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Eh8utmrD... sold 2.9703 SOL of boomer", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:58.815324Z"}
2025-05-26 18:33:59,310 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:33:59,743 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4vejd1ry... sold 1.4851 SOL of challenge", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:33:59.742795Z"}
2025-05-26 18:34:02,951 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8AXFkxT8... sold 1.9802 SOL of PLAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:02.950598Z"}
2025-05-26 18:34:03,030 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GxjBNaQc... sold 0.9901 SOL of \ud83d\udd25", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:03.029953Z"}
2025-05-26 18:34:04,683 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DFe9ud8i... sold 1.6864 SOL of boomer", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:04.683520Z"}
2025-05-26 18:34:05,161 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7AJFzqaB... sold 1.9802 SOL of Sandwich", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:05.161067Z"}
2025-05-26 18:34:09,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:34:15,475 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gnn6JcAe... sold 1.9802 SOL of McKOL's", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:15.474828Z"}
2025-05-26 18:34:15,548 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5r1HzfMn... sold 1.9802 SOL of god", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:15.548431Z"}
2025-05-26 18:34:18,300 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Dj1cUYo... sold 0.9901 SOL of gmail", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:18.300565Z"}
2025-05-26 18:34:19,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:34:21,381 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 82eYQw9f... sold 1.6864 SOL of boomer", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:21.381178Z"}
2025-05-26 18:34:22,626 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5w6Le7UN... sold 1.9000 SOL of Sandwich", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:22.626750Z"}
2025-05-26 18:34:22,835 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ADC1QV9r... sold 1.9802 SOL of O-SCIENCE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:22.835585Z"}
2025-05-26 18:34:24,069 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: E4zV6o7Y... sold 1.9000 SOL of ENEG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:24.069552Z"}
2025-05-26 18:34:27,216 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HTpnNqP1... sold 3.0000 SOL of Explode", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:27.216450Z"}
2025-05-26 18:34:27,258 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4mphVxSw... sold 1.2000 SOL of Crypto", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:27.258657Z"}
2025-05-26 18:34:29,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 18:34:29,691 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of seed", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:29.691479Z"}
2025-05-26 18:34:31,356 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dr8Ej9Vd... sold 1.1881 SOL of koko", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:31.356694Z"}
2025-05-26 18:34:31,684 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CEUA7zVo... sold 1.7822 SOL of Tension", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:31.684576Z"}
2025-05-26 18:34:31,734 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6x13RjmW... sold 2.9703 SOL of 4", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:31.734743Z"}
2025-05-26 18:34:32,786 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5Yderdf4... sold 0.0010 SOL of FUNNYS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-26T22:34:32.786440Z"}
2025-05-26 21:34:07,083 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:07.081585Z"}
2025-05-26 21:34:07,493 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-27T01:34:07.493422Z"}
2025-05-26 21:34:07,518 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:07.512982Z"}
2025-05-26 21:34:07,603 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:07.602926Z"}
2025-05-26 21:34:07,608 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-27T01:34:07.607973Z"}
2025-05-26 21:34:07,618 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:34:07.618314Z"}
2025-05-26 21:34:08,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 21:34:08,380 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:34:08.379983Z"}
2025-05-26 21:34:08,389 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.389237Z"}
2025-05-26 21:34:08,397 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.397562Z"}
2025-05-26 21:34:08,401 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.401803Z"}
2025-05-26 21:34:08,409 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.409061Z"}
2025-05-26 21:34:08,419 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.418855Z"}
2025-05-26 21:34:08,424 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.423479Z"}
2025-05-26 21:34:08,431 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.430908Z"}
2025-05-26 21:34:08,442 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.442385Z"}
2025-05-26 21:34:08,590 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-27T01:34:08.590575Z"}
2025-05-26 21:34:08,705 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-27T01:34:08.705195Z"}
2025-05-26 21:34:08,721 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:08.721614Z"}
2025-05-26 21:34:08,729 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.729360Z"}
2025-05-26 21:34:08,735 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.735414Z"}
2025-05-26 21:34:08,747 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.747490Z"}
2025-05-26 21:34:08,750 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:34:08.750457Z"}
2025-05-26 21:34:08,824 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-27T01:34:08.824715Z"}
2025-05-26 21:34:08,836 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:08.835951Z"}
2025-05-26 21:34:09,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 21:34:09,059 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 21:34:09,067 - telegram.ext.Application - INFO - Application started
2025-05-26 21:34:09,074 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:09.074465Z"}
2025-05-26 21:34:09,122 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:09.122746Z"}
2025-05-26 21:34:09,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 21:34:09,175 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:34:09.175492Z"}
2025-05-26 21:34:09,607 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:34:10,332 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3sGp5BTB... sold 0.9901 SOL of Pepe", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:10.331981Z"}
2025-05-26 21:34:10,926 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 21:34:11,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 21:34:11,470 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 21:34:19,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:34:29,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:34:29,280 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DVxREzTf... sold 0.9901 SOL of Buy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:29.280271Z"}
2025-05-26 21:34:29,633 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 21:34:29,846 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/editMessageText "HTTP/1.1 200 OK"
2025-05-26 21:34:34,336 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DaPHw3Aa... sold 1.9802 SOL of KIPLING", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:34.336654Z"}
2025-05-26 21:34:38,540 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 87uFhYfB... sold 0.4950 SOL of Chihuahua", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:38.540177Z"}
2025-05-26 21:34:38,738 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FYg5pkrV... sold 0.9901 SOL of WGM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:38.737906Z"}
2025-05-26 21:34:39,440 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:34:40,300 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BUjrbvEa... sold 0.2970 SOL of MIG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:40.300612Z"}
2025-05-26 21:34:42,605 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3rYj2dTh... sold 4.0000 SOL of COBIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:42.605684Z"}
2025-05-26 21:34:42,713 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of COBIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:42.713241Z"}
2025-05-26 21:34:42,809 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of COBIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:42.809038Z"}
2025-05-26 21:34:42,869 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3rYj2dTh... sold 4.0000 SOL of COBIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:42.869196Z"}
2025-05-26 21:34:42,963 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of COBIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:42.963074Z"}
2025-05-26 21:34:43,025 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GidNJ3wV... sold 1.9802 SOL of HIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:43.025202Z"}
2025-05-26 21:34:43,140 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of COBIE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:43.140441Z"}
2025-05-26 21:34:46,330 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fv7oXSAM... sold 0.9901 SOL of MOONTIME", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:46.330632Z"}
2025-05-26 21:34:49,542 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:34:53,305 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dn7D6VYZ... sold 0.5941 SOL of Pepeism", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:34:53.305226Z"}
2025-05-26 21:34:59,785 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:35:00,477 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8cztdSB5... sold 1.7822 SOL of DOODLE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:00.477419Z"}
2025-05-26 21:35:06,408 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7auhf25y... sold 0.9901 SOL of SUCK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:06.408002Z"}
2025-05-26 21:35:07,158 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4tVUBuCX... sold 0.2500 SOL of Delusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:07.158104Z"}
2025-05-26 21:35:09,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:35:20,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:35:25,898 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BeghB1gn... sold 0.8500 SOL of BITMAMA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:25.898478Z"}
2025-05-26 21:35:27,217 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EuBz8hqw... sold 1.9802 SOL of USUG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:27.216888Z"}
2025-05-26 21:35:29,890 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: E1ptvT7J... sold 0.8911 SOL of IRF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:29.890734Z"}
2025-05-26 21:35:30,198 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:35:33,212 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7jh9swux... sold 0.8712 SOL of JBC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:33.212317Z"}
2025-05-26 21:35:40,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:35:43,146 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6tFKgMpj... sold 1.2871 SOL of RAJ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:43.146273Z"}
2025-05-26 21:35:43,177 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 69BUCzaR... sold 0.0100 SOL of RGM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:43.177630Z"}
2025-05-26 21:35:43,209 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9PZD8BzQ... sold 0.6931 SOL of  LamboTes", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:43.209508Z"}
2025-05-26 21:35:43,468 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G7NvZKjo... sold 2.9703 SOL of MARIA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:43.468349Z"}
2025-05-26 21:35:47,094 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9xxvbqtL... sold 12.4424 SOL of DeepFates", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:47.094660Z"}
2025-05-26 21:35:49,272 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Bq3CD117... sold 0.1485 SOL of ASS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:49.272019Z"}
2025-05-26 21:35:50,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:35:54,548 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G9zq6Nxo... sold 1.9802 SOL of Delusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:54.548379Z"}
2025-05-26 21:35:54,662 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HbNFEASd... sold 0.9901 SOL of Dogdragon", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:35:54.662145Z"}
2025-05-26 21:36:00,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:36:01,236 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AuEtkpXz... sold 0.9901 SOL of LATINA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:01.236078Z"}
2025-05-26 21:36:03,756 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ER561nUd... sold 0.9901 SOL of WHATIF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:03.756461Z"}
2025-05-26 21:36:10,748 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:36:20,840 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:36:23,939 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:36:24,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 21:36:34,094 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:36:37,479 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AbPcPUYa... sold 1.2871 SOL of payeer", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:37.479466Z"}
2025-05-26 21:36:38,204 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:36:38,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 21:36:40,196 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FHnqJnHc... sold 1.1881 SOL of FRIENDS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:40.196316Z"}
2025-05-26 21:36:41,571 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BP8Lsc7j... sold 2.0000 SOL of death", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:41.571741Z"}
2025-05-26 21:36:48,532 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:36:52,360 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6RXy5pbP... sold 0.0000 SOL of WLH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:52.360519Z"}
2025-05-26 21:36:53,351 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2gqiG6U5... sold 0.1980 SOL of Frog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:53.350971Z"}
2025-05-26 21:36:53,604 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5VWptgmE... sold 0.0000 SOL of WLH", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:53.602968Z"}
2025-05-26 21:36:55,193 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ZU6n7n35... sold 0.0099 SOL of Macron fal", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:55.193506Z"}
2025-05-26 21:36:56,828 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4CcbTAt5... sold 0.0400 SOL of LIVENAKED", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:36:56.828159Z"}
2025-05-26 21:36:58,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:37:01,687 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Bn1uTeFk... sold 0.0000 SOL of WHATIF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:01.687537Z"}
2025-05-26 21:37:03,487 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ASztGAXm... sold 0.9901 SOL of fremp", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:03.486905Z"}
2025-05-26 21:37:06,046 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CmboHmvy... sold 0.9901 SOL of frov", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:06.046137Z"}
2025-05-26 21:37:08,712 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:37:10,653 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: APpz1UgL... sold 1.3861 SOL of EZ1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:10.653358Z"}
2025-05-26 21:37:12,028 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4CcbTAt5... sold 0.0400 SOL of WHORE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:12.028141Z"}
2025-05-26 21:37:12,064 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DA1aK5r4... sold 1.9802 SOL of niceonebro", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:12.064779Z"}
2025-05-26 21:37:12,192 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5VWptgmE... sold 0.0000 SOL of 3QJ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:12.191070Z"}
2025-05-26 21:37:12,696 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: J4hqx6XL... sold 1.1880 SOL of GT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:12.696008Z"}
2025-05-26 21:37:12,815 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: C3a5z9TW... sold 2.1300 SOL of WHATIF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:12.815164Z"}
2025-05-26 21:37:16,164 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Cj77tqGn... sold 0.9901 SOL of BEE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:16.164415Z"}
2025-05-26 21:37:16,236 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9rkmZD29... sold 22.7723 SOL of Kickcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:16.235367Z"}
2025-05-26 21:37:16,916 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7auhf25y... sold 0.9901 SOL of IBM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:16.916479Z"}
2025-05-26 21:37:18,823 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:37:23,860 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4kPMKH9F... sold 0.9901 SOL of DVMCHGKIFY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:23.860146Z"}
2025-05-26 21:37:29,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:37:31,984 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4CcbTAt5... sold 0.0400 SOL of BIGBOOTY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:31.984120Z"}
2025-05-26 21:37:36,062 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CJLy8YhN... sold 1.2871 SOL of 1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:36.062613Z"}
2025-05-26 21:37:39,171 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:37:39,833 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DFtihuTN... sold 0.0990 SOL of FLIPILISK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:37:39.833883Z"}
2025-05-26 21:46:24,765 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:24.765040Z"}
2025-05-26 21:46:24,851 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-27T01:46:24.851689Z"}
2025-05-26 21:46:24,860 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:24.859920Z"}
2025-05-26 21:46:24,864 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:24.864345Z"}
2025-05-26 21:46:24,868 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-27T01:46:24.868444Z"}
2025-05-26 21:46:24,871 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:46:24.871269Z"}
2025-05-26 21:46:25,434 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 21:46:25,443 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:46:25.443225Z"}
2025-05-26 21:46:25,448 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.448056Z"}
2025-05-26 21:46:25,462 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.462384Z"}
2025-05-26 21:46:25,464 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.464726Z"}
2025-05-26 21:46:25,467 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.467054Z"}
2025-05-26 21:46:25,472 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.472292Z"}
2025-05-26 21:46:25,474 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.474649Z"}
2025-05-26 21:46:25,481 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.480942Z"}
2025-05-26 21:46:25,491 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.491524Z"}
2025-05-26 21:46:25,528 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-27T01:46:25.528442Z"}
2025-05-26 21:46:25,533 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-27T01:46:25.533076Z"}
2025-05-26 21:46:25,540 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:25.540031Z"}
2025-05-26 21:46:25,547 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.547000Z"}
2025-05-26 21:46:25,550 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.550878Z"}
2025-05-26 21:46:25,555 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.555547Z"}
2025-05-26 21:46:25,588 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:46:25.588728Z"}
2025-05-26 21:46:25,603 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-27T01:46:25.602755Z"}
2025-05-26 21:46:25,617 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:25.617362Z"}
2025-05-26 21:46:25,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 21:46:25,815 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 21:46:25,821 - telegram.ext.Application - INFO - Application started
2025-05-26 21:46:25,837 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:25.837542Z"}
2025-05-26 21:46:25,878 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:25.877974Z"}
2025-05-26 21:46:25,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 21:46:25,950 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:46:25.950269Z"}
2025-05-26 21:46:31,283 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5S9qzJhS... sold 0.9901 SOL of SOL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:31.283823Z"}
2025-05-26 21:46:36,222 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:46:46,336 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:46:49,100 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GcKJRuy6... sold 0.0100 SOL of SailMoon3", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:49.100650Z"}
2025-05-26 21:46:50,333 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6ZCdVHoa... sold 0.8500 SOL of chillcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:50.333639Z"}
2025-05-26 21:46:51,786 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2oyKqHbY... sold 0.8712 SOL of MCrabz", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:51.786094Z"}
2025-05-26 21:46:54,031 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2yEjC74Y... sold 0.3762 SOL of BabyPepe", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:54.031185Z"}
2025-05-26 21:46:55,122 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of CA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:55.122795Z"}
2025-05-26 21:46:55,859 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2W4HjumF... sold 5.0000 SOL of PATTON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:55.859470Z"}
2025-05-26 21:46:56,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:46:58,485 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CK7PTE9v... sold 0.9901 SOL of GCC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:46:58.485217Z"}
2025-05-26 21:47:00,774 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G7NvZKjo... sold 2.9703 SOL of OG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:00.774468Z"}
2025-05-26 21:47:05,914 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8ukiqUz7... sold 1.4356 SOL of KOGt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:05.914802Z"}
2025-05-26 21:47:06,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:47:08,585 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DuCKvyo1... sold 1.4301 SOL of Grrrr", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:08.585261Z"}
2025-05-26 21:47:12,178 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2hYFC3By... sold 2.0937 SOL of testttt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:12.178826Z"}
2025-05-26 21:47:15,800 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AYSwEYEZ... sold 1.2871 SOL of Chii-san", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:15.799689Z"}
2025-05-26 21:47:15,988 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Bk8NT5X... sold 1.9802 SOL of Test", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:15.988008Z"}
2025-05-26 21:47:16,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:47:19,517 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: skiFC6YB... sold 1.4851 SOL of WHYNOT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:19.517737Z"}
2025-05-26 21:47:22,893 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BkjVJmaN... sold 1.4851 SOL of HuXoPsy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:22.893565Z"}
2025-05-26 21:47:26,848 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:47:28,380 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gw14GFPi... sold 2.9709 SOL of gay", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:28.380373Z"}
2025-05-26 21:47:33,746 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8bkXpaM3... sold 0.0000 SOL of WEBKINZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:33.746783Z"}
2025-05-26 21:47:36,054 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G45wjZRD... sold 0.0650 SOL of MAGIC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:36.054479Z"}
2025-05-26 21:47:36,834 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GGY9krup... sold 0.4950 SOL of Chopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:36.834825Z"}
2025-05-26 21:47:36,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:47:37,049 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6ruvDYrD... sold 1.9802 SOL of FAG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:37.049905Z"}
2025-05-26 21:47:37,581 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:47:37,950 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7auhf25y... sold 0.9901 SOL of MicroStrat", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:37.950080Z"}
2025-05-26 21:47:37,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 21:47:41,632 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: E4qjjiDn... sold 1.7723 SOL of BUYUP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:41.631921Z"}
2025-05-26 21:47:41,879 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BNH9KfTA... sold 0.4950 SOL of Conan", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:41.879559Z"}
2025-05-26 21:47:43,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:47:43,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 21:47:43,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/editMessageText "HTTP/1.1 400 Bad Request"
2025-05-26 21:47:43,897 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_application.py", line 1234, in process_update
    await coroutine
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_basehandler.py", line 157, in handle_update
    return await self.callback(update, context)
  File "/Users/<USER>/Documents/augment-projects/solana_trading_bot/src/telegram_bot/bloom_bot.py", line 381, in button_callback
    await query.edit_message_text(status_text, parse_mode="Markdown")
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_callbackquery.py", line 241, in edit_message_text
    return await self.message.edit_text(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_message.py", line 2536, in edit_text
    return await self.get_bot().edit_message_text(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 1473, in edit_message_text
    return await super().edit_message_text(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 3288, in edit_message_text
    return await self._send_message(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 522, in _send_message
    result = await super()._send_message(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 703, in _send_message
    result = await self._post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 379, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Can't parse entities: can't find end of the entity starting at byte offset 20
2025-05-26 21:47:50,583 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Y743WcfZ... sold 0.0099 SOL of PJM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:50.583560Z"}
2025-05-26 21:47:53,515 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5ueQKeFe... sold 1.9802 SOL of BaitCoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:47:53.515401Z"}
2025-05-26 21:47:53,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:48:03,750 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:48:04,690 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CJDUrp8F... sold 0.9901 SOL of FNF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:04.690728Z"}
2025-05-26 21:48:06,271 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gw14GFPi... sold 2.9709 SOL of STD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:06.271732Z"}
2025-05-26 21:48:06,780 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AEKhAf11... sold 1.7402 SOL of Gorken", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:06.780379Z"}
2025-05-26 21:48:11,446 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F18gGwiB... sold 2.0000 SOL of DEGREE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:11.446442Z"}
2025-05-26 21:48:12,549 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6hSRBE15... sold 0.9901 SOL of Dreamcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:12.549025Z"}
2025-05-26 21:48:13,888 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:48:17,742 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7KWrRKUU... sold 0.0396 SOL of The Balls", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:17.742453Z"}
2025-05-26 21:48:18,600 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Gw14GFPi... sold 2.9709 SOL of STD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:18.600401Z"}
2025-05-26 21:48:20,933 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 56LpjJDt... sold 1.4356 SOL of mYW", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:20.933352Z"}
2025-05-26 21:48:23,982 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:48:30,783 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2GnxwDPF... sold 1.9802 SOL of Murphy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:30.783767Z"}
2025-05-26 21:48:32,952 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2hYFC3By... sold 2.0937 SOL of ELOQ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:32.951945Z"}
2025-05-26 21:48:34,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:48:35,698 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Bixjpq2f... sold 0.8712 SOL of SSWAP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:35.698359Z"}
2025-05-26 21:48:37,498 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2RsuU1kf... sold 1.3000 SOL of Craze", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:37.498788Z"}
2025-05-26 21:48:37,532 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3NtngHEB... sold 0.0041 SOL of latit", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:37.532598Z"}
2025-05-26 21:48:40,325 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GmzB9WRG... sold 1.0000 SOL of TSULALA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:40.325628Z"}
2025-05-26 21:48:41,535 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2sFgSuCR... sold 0.9901 SOL of PEBUBU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:41.535366Z"}
2025-05-26 21:48:42,180 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:48:42,690 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 21:48:49,223 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:48:49,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-26 21:48:56,587 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Be1GAo7U... sold 0.4950 SOL of SLAP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:56.587821Z"}
2025-05-26 21:48:59,047 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2mamN4vC... sold 0.3561 SOL of Sochi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:48:59.047626Z"}
2025-05-26 21:48:59,352 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:56:42,276 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:56:42.276549Z"}
2025-05-26 21:56:42,409 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-27T01:56:42.409037Z"}
2025-05-26 21:56:42,466 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:56:42.466228Z"}
2025-05-26 21:56:42,506 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:56:42.506294Z"}
2025-05-26 21:56:42,525 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-27T01:56:42.525624Z"}
2025-05-26 21:56:42,556 - SolanaTradingBot - ERROR - {"event": "Failed to initialize bot: 'FeatureFlags' object has no attribute 'get'", "logger": "SolanaTradingBot", "level": "error", "timestamp": "2025-05-27T01:56:42.550460Z"}
2025-05-26 21:56:42,608 - solana_trading_bot - ERROR - {"event": "Bot crashed: 'FeatureFlags' object has no attribute 'get'", "logger": "solana_trading_bot", "level": "error", "timestamp": "2025-05-27T01:56:42.608106Z"}
2025-05-26 21:56:42,756 - SolanaTradingBot - INFO - {"event": "Stopping Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:56:42.756592Z"}
2025-05-26 21:56:42,791 - SolanaClient - INFO - {"event": "Solana client closed", "logger": "SolanaClient", "level": "info", "timestamp": "2025-05-27T01:56:42.791298Z"}
2025-05-26 21:56:42,836 - TransactionMonitor - INFO - {"event": "Transaction monitoring stopped", "logger": "TransactionMonitor", "level": "info", "timestamp": "2025-05-27T01:56:42.836410Z"}
2025-05-26 21:56:42,912 - PumpFunMonitor - INFO - {"event": "Pump.fun monitoring stopped", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:56:42.912235Z"}
2025-05-26 21:56:42,994 - NotificationManager - INFO - {"event": "Notification manager stopped", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-27T01:56:42.994397Z"}
2025-05-26 21:56:43,002 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot stopped", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:56:43.002644Z"}
2025-05-26 21:57:21,244 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.244140Z"}
2025-05-26 21:57:21,295 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-27T01:57:21.295502Z"}
2025-05-26 21:57:21,297 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.297867Z"}
2025-05-26 21:57:21,325 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.325139Z"}
2025-05-26 21:57:21,342 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-27T01:57:21.342198Z"}
2025-05-26 21:57:21,378 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:57:21.378506Z"}
2025-05-26 21:57:21,861 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-26 21:57:21,867 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:57:21.867289Z"}
2025-05-26 21:57:21,871 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.871770Z"}
2025-05-26 21:57:21,875 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.875880Z"}
2025-05-26 21:57:21,880 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.880538Z"}
2025-05-26 21:57:21,892 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.892130Z"}
2025-05-26 21:57:21,894 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.894339Z"}
2025-05-26 21:57:21,896 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.896466Z"}
2025-05-26 21:57:21,909 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.909695Z"}
2025-05-26 21:57:21,912 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.912804Z"}
2025-05-26 21:57:21,943 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-27T01:57:21.942992Z"}
2025-05-26 21:57:21,945 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-27T01:57:21.945749Z"}
2025-05-26 21:57:21,955 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:21.955022Z"}
2025-05-26 21:57:21,958 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.958214Z"}
2025-05-26 21:57:21,972 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.964223Z"}
2025-05-26 21:57:21,979 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.979042Z"}
2025-05-26 21:57:21,988 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-27T01:57:21.988070Z"}
2025-05-26 21:57:21,991 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-27T01:57:21.991548Z"}
2025-05-26 21:57:21,994 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:21.994237Z"}
2025-05-26 21:57:22,137 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:22.137336Z"}
2025-05-26 21:57:22,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-26 21:57:22,354 - apscheduler.scheduler - INFO - Scheduler started
2025-05-26 21:57:22,359 - telegram.ext.Application - INFO - Application started
2025-05-26 21:57:22,372 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:22.371802Z"}
2025-05-26 21:57:22,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-26 21:57:22,475 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-27T01:57:22.474682Z"}
2025-05-26 21:57:23,760 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6UM1QLxe... sold 1.9802 SOL of MT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:23.760315Z"}
2025-05-26 21:57:26,210 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: B2mpX5cw... sold 0.0100 SOL of Fusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:26.210685Z"}
2025-05-26 21:57:26,249 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8Nsojm73... sold 0.0100 SOL of Fusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:26.249769Z"}
2025-05-26 21:57:26,412 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AgvAqpeU... sold 0.0100 SOL of Fusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:26.412689Z"}
2025-05-26 21:57:27,752 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F6zXT5M6... sold 0.0100 SOL of Fusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:27.752251Z"}
2025-05-26 21:57:27,834 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9RpoziPm... sold 0.0100 SOL of Fusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:27.833988Z"}
2025-05-26 21:57:27,886 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 66YfLinb... sold 0.0100 SOL of Fusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:27.886729Z"}
2025-05-26 21:57:28,020 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6dgFT31L... sold 0.0100 SOL of Fusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:28.019746Z"}
2025-05-26 21:57:28,123 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F6WqNjGb... sold 0.0100 SOL of Fusion", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:28.122834Z"}
2025-05-26 21:57:28,200 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9hXMaRys... sold 1.3861 SOL of Graph", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:28.200182Z"}
2025-05-26 21:57:30,818 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EtV6GqY9... sold 1.4851 SOL of NEVER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:30.818445Z"}
2025-05-26 21:57:31,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:57:31,757 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9vaYZmAj... sold 3.4653 SOL of IRYSM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:31.757515Z"}
2025-05-26 21:57:31,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-26 21:57:33,892 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9rkmZD29... sold 21.0198 SOL of Kickcoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:33.892868Z"}
2025-05-26 21:57:34,095 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AQu1RjiF... sold 0.9901 SOL of HOLSTER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:34.095065Z"}
2025-05-26 21:57:35,833 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AJdavq9H... sold 4.9505 SOL of FART", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:35.832987Z"}
2025-05-26 21:57:38,831 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: E4qjjiDn... sold 1.8317 SOL of FLYING", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:38.831571Z"}
2025-05-26 21:57:41,568 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GmbfzQRV... sold 0.2970 SOL of MENCOIN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:41.568327Z"}
2025-05-26 21:57:41,700 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:57:46,485 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6WjJfqoR... sold 2.4752 SOL of Bepe", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:46.485402Z"}
2025-05-26 21:57:49,560 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7auhf25y... sold 0.9901 SOL of ONE DAY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:49.559939Z"}
2025-05-26 21:57:51,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-26 21:57:54,473 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7KWrRKUU... sold 0.0099 SOL of Ali ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-27T01:57:54.473724Z"}
2025-05-29 13:39:50,132 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:50.132057Z"}
2025-05-29 13:39:50,295 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-29T17:39:50.295598Z"}
2025-05-29 13:39:50,298 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:50.298086Z"}
2025-05-29 13:39:50,307 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:50.307825Z"}
2025-05-29 13:39:50,310 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-29T17:39:50.310808Z"}
2025-05-29 13:39:50,345 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-29T17:39:50.345519Z"}
2025-05-29 13:39:50,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-29 13:39:50,927 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-29T17:39:50.927506Z"}
2025-05-29 13:39:50,929 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:50.929846Z"}
2025-05-29 13:39:50,941 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:50.941068Z"}
2025-05-29 13:39:50,944 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:50.944792Z"}
2025-05-29 13:39:50,979 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:50.978940Z"}
2025-05-29 13:39:50,991 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:50.991729Z"}
2025-05-29 13:39:51,060 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:51.060281Z"}
2025-05-29 13:39:51,063 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:51.063251Z"}
2025-05-29 13:39:51,072 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:51.068626Z"}
2025-05-29 13:39:51,115 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-29T17:39:51.115285Z"}
2025-05-29 13:39:51,128 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-29T17:39:51.128542Z"}
2025-05-29 13:39:51,141 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:39:51.141155Z"}
2025-05-29 13:39:51,146 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:51.146832Z"}
2025-05-29 13:39:51,158 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:51.158577Z"}
2025-05-29 13:39:51,170 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:51.170204Z"}
2025-05-29 13:39:51,259 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:39:51.259792Z"}
2025-05-29 13:39:51,265 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-29T17:39:51.264980Z"}
2025-05-29 13:39:51,270 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:39:51.270361Z"}
2025-05-29 13:39:51,412 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-29 13:39:51,427 - apscheduler.scheduler - INFO - Scheduler started
2025-05-29 13:39:51,431 - telegram.ext.Application - INFO - Application started
2025-05-29 13:39:51,446 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:39:51.446089Z"}
2025-05-29 13:39:51,470 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:39:51.470603Z"}
2025-05-29 13:39:51,528 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-29 13:39:51,535 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-29T17:39:51.535772Z"}
2025-05-29 13:39:51,852 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:39:52,359 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-29 13:39:56,782 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4AwQqeqc... sold 4.0000 SOL of GOAT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:39:56.781903Z"}
2025-05-29 13:40:01,951 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:40:09,849 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Adq34c2s... sold 3.0000 SOL of HOLD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:09.849074Z"}
2025-05-29 13:40:09,872 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 46ozLRrH... sold 0.0000 SOL of PEPE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:09.872636Z"}
2025-05-29 13:40:09,907 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8v2kjaZH... sold 2.9703 SOL of PEPE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:09.907362Z"}
2025-05-29 13:40:10,072 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4AwQqeqc... sold 3.0000 SOL of GOAT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:10.072069Z"}
2025-05-29 13:40:11,382 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: D2GSVjZW... sold 0.0000 SOL of PEPE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:11.382046Z"}
2025-05-29 13:40:12,123 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:40:14,022 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GyiAMVLu... sold 1.9802 SOL of PIGGYBANK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:14.022047Z"}
2025-05-29 13:40:15,549 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ETUd3rYr... sold 14.8515 SOL of LUCK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:15.549780Z"}
2025-05-29 13:40:17,672 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:40:18,075 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/answerCallbackQuery "HTTP/1.1 200 OK"
2025-05-29 13:40:20,151 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HzabkrJy... sold 2.1782 SOL of G.O.A.T", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:20.151701Z"}
2025-05-29 13:40:20,709 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4AwQqeqc... sold 3.0000 SOL of GOAT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:20.709538Z"}
2025-05-29 13:40:22,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:40:23,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/sendMessage "HTTP/1.1 200 OK"
2025-05-29 13:40:27,109 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dtrqctmy... sold 0.7921 SOL of Camel", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:27.109585Z"}
2025-05-29 13:40:30,402 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 49bVau2i... sold 1.9802 SOL of GGoon", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:30.402598Z"}
2025-05-29 13:40:33,049 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:40:34,014 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5uWonXHf... sold 1.2376 SOL of bitoink", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:34.014361Z"}
2025-05-29 13:40:34,128 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6zX8Q7T8... sold 0.1980 SOL of LC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:34.127620Z"}
2025-05-29 13:40:34,247 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BrRYpvhp... sold 1.1386 SOL of R/DOGE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:34.246958Z"}
2025-05-29 13:40:37,350 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8v2kjaZH... sold 2.9703 SOL of NIGGER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:37.350876Z"}
2025-05-29 13:40:37,549 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9cnTMkLv... sold 0.2970 SOL of CARIBO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:37.549614Z"}
2025-05-29 13:40:38,287 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FsjddZgX... sold 1.2871 SOL of ACCBIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:38.287490Z"}
2025-05-29 13:40:41,961 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4LVFuib4... sold 1.9802 SOL of $PEPPE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:41.961449Z"}
2025-05-29 13:40:42,130 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Edim6J4V... sold 1.5800 SOL of CHONK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:42.130711Z"}
2025-05-29 13:40:43,141 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:40:43,189 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of PRN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:43.188950Z"}
2025-05-29 13:40:44,275 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FU6iTK4r... sold 0.0198 SOL of BULAN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:44.275562Z"}
2025-05-29 13:40:45,087 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GhQrL9cy... sold 0.7921 SOL of Bacon", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:45.087833Z"}
2025-05-29 13:40:45,856 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Fp8eWtjQ... sold 84.1584 SOL of VIVID", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:45.856263Z"}
2025-05-29 13:40:51,828 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6gSc3Kip... sold 0.9901 SOL of r/pig", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:51.828515Z"}
2025-05-29 13:40:53,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:40:53,878 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AzN9dbnE... sold 1.0330 SOL of GLRD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:53.878210Z"}
2025-05-29 13:40:55,428 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3PC5geEF... sold 1.9802 SOL of MISS PIGGY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:40:55.428037Z"}
2025-05-29 13:41:15,789 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:15.789615Z"}
2025-05-29 13:41:15,918 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-29T17:41:15.917981Z"}
2025-05-29 13:41:15,924 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:15.923963Z"}
2025-05-29 13:41:15,936 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:15.936282Z"}
2025-05-29 13:41:15,953 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-29T17:41:15.953042Z"}
2025-05-29 13:41:16,057 - BloomBot - INFO - {"event": "Added authorized user: 1484472507", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-29T17:41:16.057807Z"}
2025-05-29 13:41:16,705 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/setMyCommands "HTTP/1.1 200 OK"
2025-05-29 13:41:16,708 - BloomBot - INFO - {"event": "Bloom Telegram bot initialized successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-29T17:41:16.708642Z"}
2025-05-29 13:41:16,721 - SolanaTradingBot - INFO - {"event": "Telegram bot initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:16.721101Z"}
2025-05-29 13:41:16,724 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:16.724865Z"}
2025-05-29 13:41:16,790 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:16.789854Z"}
2025-05-29 13:41:16,820 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:16.820343Z"}
2025-05-29 13:41:16,823 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:16.823337Z"}
2025-05-29 13:41:16,825 - SolanaTradingBot - INFO - {"event": "Telegram bot started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:16.825681Z"}
2025-05-29 13:41:16,855 - SolanaTradingBot - INFO - {"event": "Notification manager started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:16.855819Z"}
2025-05-29 13:41:16,858 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:16.858381Z"}
2025-05-29 13:41:16,932 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-29T17:41:16.931862Z"}
2025-05-29 13:41:16,956 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-29T17:41:16.956245Z"}
2025-05-29 13:41:17,007 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:17.007789Z"}
2025-05-29 13:41:17,066 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:17.066482Z"}
2025-05-29 13:41:17,146 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:17.143603Z"}
2025-05-29 13:41:17,192 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:17.192610Z"}
2025-05-29 13:41:17,206 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-29T17:41:17.206489Z"}
2025-05-29 13:41:17,223 - NotificationManager - INFO - {"event": "Notification manager started", "logger": "NotificationManager", "level": "info", "timestamp": "2025-05-29T17:41:17.223603Z"}
2025-05-29 13:41:17,262 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:17.261985Z"}
2025-05-29 13:41:17,436 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:17.436016Z"}
2025-05-29 13:41:17,456 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getMe "HTTP/1.1 200 OK"
2025-05-29 13:41:17,468 - apscheduler.scheduler - INFO - Scheduler started
2025-05-29 13:41:17,474 - telegram.ext.Application - INFO - Application started
2025-05-29 13:41:17,492 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:17.492100Z"}
2025-05-29 13:41:17,576 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/deleteWebhook "HTTP/1.1 200 OK"
2025-05-29 13:41:17,589 - BloomBot - INFO - {"event": "Bloom Telegram bot started successfully", "logger": "BloomBot", "level": "info", "timestamp": "2025-05-29T17:41:17.589569Z"}
2025-05-29 13:41:18,032 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4AwQqeqc... sold 5.0000 SOL of DYOR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:18.031889Z"}
2025-05-29 13:41:20,358 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dwf1RDeY... sold 2.9703 SOL of Stars", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:20.357997Z"}
2025-05-29 13:41:25,829 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9h5g3eru... sold 11.3861 SOL of VIVID", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:25.829805Z"}
2025-05-29 13:41:26,197 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DQxJUUSD... sold 1.9802 SOL of UMU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:26.196946Z"}
2025-05-29 13:41:26,280 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HWhGTQhQ... sold 0.4950 SOL of G.O.A.T", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:26.280158Z"}
2025-05-29 13:41:27,210 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5fvS7DdW... sold 1.8713 SOL of R/DOGE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:27.210896Z"}
2025-05-29 13:41:28,046 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:41:31,662 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 47vVSDWp... sold 0.0030 SOL of CNBZZZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:31.662600Z"}
2025-05-29 13:41:31,702 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5Ba9aULT... sold 12.4253 SOL of HOPEJR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:31.702539Z"}
2025-05-29 13:41:32,341 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BWzA5Any... sold 0.0297 SOL of lilpigs", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:32.341432Z"}
2025-05-29 13:41:34,541 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of ILYSOL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:34.541438Z"}
2025-05-29 13:41:37,414 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3cmzFg32... sold 0.2077 SOL of OBC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:37.414785Z"}
2025-05-29 13:41:38,328 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:41:39,015 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5GYZCuny... sold 0.9901 SOL of PIG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:39.015354Z"}
2025-05-29 13:41:39,110 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8v2kjaZH... sold 2.9703 SOL of omkney", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:39.109983Z"}
2025-05-29 13:41:39,798 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G7GwYRrB... sold 1.5733 SOL of Card", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:39.798469Z"}
2025-05-29 13:41:41,134 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 66666KoH... sold 1.9802 SOL of Dr.Squatch", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:41.134134Z"}
2025-05-29 13:41:45,948 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Tf6akNs... sold 1.9802 SOL of PVP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:45.945943Z"}
2025-05-29 13:41:48,078 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EWeqJ5zS... sold 1.9000 SOL of masi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:48.077927Z"}
2025-05-29 13:41:49,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7541277392:AAHGkfhtw_6d5HJOBd181p6sQl5FbRXlrAk/getUpdates "HTTP/1.1 200 OK"
2025-05-29 13:41:52,731 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: J5WHQkAr... sold 1.4663 SOL of rug", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-29T17:41:52.731775Z"}
2025-05-29 22:56:27,406 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-30T02:56:27.406376Z"}
2025-05-29 22:56:27,422 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-30T02:56:27.422773Z"}
2025-05-29 22:56:27,508 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:27.508392Z"}
2025-05-29 22:56:27,966 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-30T02:56:27.966068Z"}
2025-05-29 22:56:27,970 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:27.970598Z"}
2025-05-29 22:56:27,973 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:27.973548Z"}
2025-05-29 22:56:27,979 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-30T02:56:27.979135Z"}
2025-05-29 22:56:28,052 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.052071Z"}
2025-05-29 22:56:28,064 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.064179Z"}
2025-05-29 22:56:28,073 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.073158Z"}
2025-05-29 22:56:28,092 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.092375Z"}
2025-05-29 22:56:28,102 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.102481Z"}
2025-05-29 22:56:28,131 - SolanaTradingBot - INFO - {"event": "Running 5 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.131659Z"}
2025-05-29 22:56:28,201 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-30T02:56:28.200797Z"}
2025-05-29 22:56:28,210 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-30T02:56:28.210394Z"}
2025-05-29 22:56:28,221 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:56:28.221589Z"}
2025-05-29 22:56:28,227 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.227278Z"}
2025-05-29 22:56:28,247 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.244981Z"}
2025-05-29 22:56:28,284 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.284101Z"}
2025-05-29 22:56:28,309 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T02:56:28.308849Z"}
2025-05-29 22:56:28,356 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:56:28.356441Z"}
2025-05-29 22:56:28,563 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:56:28.563839Z"}
2025-05-29 22:56:28,592 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:56:28.592177Z"}
2025-05-29 22:56:37,874 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 1kKeYdcS... sold 1.2871 SOL of can", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:56:37.874361Z"}
2025-05-29 22:56:49,353 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7ekvx967... sold 1.4851 SOL of PIGDAQ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:56:49.353587Z"}
2025-05-29 22:57:08,314 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FtJSiHKw... sold 1.2079 SOL of Zeus", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:08.314240Z"}
2025-05-29 22:57:10,614 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 64royonh... sold 0.0495 SOL of Trump.ir", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:10.614120Z"}
2025-05-29 22:57:10,648 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 68zCgLXj... sold 0.0753 SOL of HOPE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:10.648681Z"}
2025-05-29 22:57:20,414 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5sQV99vw... sold 0.0297 SOL of TIMSPIDER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:20.414529Z"}
2025-05-29 22:57:24,790 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BX8ESC72... sold 0.0099 SOL of ZAI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:24.790010Z"}
2025-05-29 22:57:28,628 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4CNymoxV... sold 1.7822 SOL of quit", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:28.627966Z"}
2025-05-29 22:57:28,669 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ERDBEUxk... sold 0.0010 SOL of Boy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:28.669482Z"}
2025-05-29 22:57:28,735 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FCpjVWac... sold 1.6000 SOL of PIG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:28.735371Z"}
2025-05-29 22:57:29,838 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:29.838518Z"}
2025-05-29 22:57:30,858 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:30.858623Z"}
2025-05-29 22:57:35,777 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:35.776996Z"}
2025-05-29 22:57:38,107 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: C4x9978s... sold 0.9901 SOL of pig", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:38.107766Z"}
2025-05-29 22:57:38,191 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AcGXMBnE... sold 0.0990 SOL of PORKS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:38.190951Z"}
2025-05-29 22:57:39,043 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:39.043574Z"}
2025-05-29 22:57:39,778 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8t5MMFQW... sold 1.7822 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:39.778081Z"}
2025-05-29 22:57:40,585 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2DzsJusy... sold 0.2772 SOL of BBZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:40.584944Z"}
2025-05-29 22:57:44,082 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:44.082116Z"}
2025-05-29 22:57:45,361 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:45.361457Z"}
2025-05-29 22:57:47,141 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:47.141495Z"}
2025-05-29 22:57:49,462 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6ruvDYrD... sold 1.9802 SOL of NASDAQ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:49.462612Z"}
2025-05-29 22:57:52,152 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:52.152510Z"}
2025-05-29 22:57:56,323 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:56.323548Z"}
2025-05-29 22:57:59,069 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: C5EyyLhj... sold 2.9703 SOL of wdadaw", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:59.069489Z"}
2025-05-29 22:57:59,291 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:59.291255Z"}
2025-05-29 22:57:59,871 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:57:59.871050Z"}
2025-05-29 22:58:01,579 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5sr1mErq... sold 0.9901 SOL of PNL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:01.579633Z"}
2025-05-29 22:58:02,412 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:02.412922Z"}
2025-05-29 22:58:05,638 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:05.638244Z"}
2025-05-29 22:58:06,800 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H1GD2fdi... sold 1.3069 SOL of ELON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:06.800517Z"}
2025-05-29 22:58:08,497 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:08.497007Z"}
2025-05-29 22:58:10,794 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:10.794392Z"}
2025-05-29 22:58:13,255 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:13.255194Z"}
2025-05-29 22:58:17,191 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:17.191333Z"}
2025-05-29 22:58:17,540 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4e3Ykk94... sold 1.4851 SOL of KIP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:17.540652Z"}
2025-05-29 22:58:17,569 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3Xfk6ywo... sold 2.9703 SOL of na5", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:17.569803Z"}
2025-05-29 22:58:19,822 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:19.822106Z"}
2025-05-29 22:58:20,685 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:20.685217Z"}
2025-05-29 22:58:27,039 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:27.037989Z"}
2025-05-29 22:58:27,254 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:27.253870Z"}
2025-05-29 22:58:28,614 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2DprWzkJ... sold 1.0891 SOL of PEPPA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:28.614878Z"}
2025-05-29 22:58:28,706 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8aVx5pN6... sold 0.7921 SOL of HELL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:28.705950Z"}
2025-05-29 22:58:28,732 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5tN1HeFA... sold 1.9802 SOL of RATWIZARD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:28.732005Z"}
2025-05-29 22:58:32,713 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:32.713321Z"}
2025-05-29 22:58:34,199 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:34.199088Z"}
2025-05-29 22:58:34,424 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:34.423186Z"}
2025-05-29 22:58:38,301 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:38.301476Z"}
2025-05-29 22:58:39,674 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6ruvDYrD... sold 1.9802 SOL of SOLANA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:39.674079Z"}
2025-05-29 22:58:39,740 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:39.740052Z"}
2025-05-29 22:58:41,483 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:41.483086Z"}
2025-05-29 22:58:42,343 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8qhLPyWf... sold 3.0000 SOL of moonpig", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:42.343613Z"}
2025-05-29 22:58:45,696 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:45.695945Z"}
2025-05-29 22:58:47,219 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:47.219028Z"}
2025-05-29 22:58:49,517 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CKqAgDzf... sold 1.2970 SOL of JERRYBEANS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:49.517400Z"}
2025-05-29 22:58:51,652 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5XYkpdKU... sold 0.9901 SOL of hachi", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:51.652367Z"}
2025-05-29 22:58:51,959 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:51.959069Z"}
2025-05-29 22:58:54,096 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:54.096481Z"}
2025-05-29 22:58:56,476 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of NOTPLAN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:56.475297Z"}
2025-05-29 22:58:56,673 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:56.673885Z"}
2025-05-29 22:58:58,521 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:58:58.521254Z"}
2025-05-29 22:59:00,103 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:00.103084Z"}
2025-05-29 22:59:02,098 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:02.098757Z"}
2025-05-29 22:59:03,673 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:03.673742Z"}
2025-05-29 22:59:06,016 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:06.015845Z"}
2025-05-29 22:59:07,011 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:07.011557Z"}
2025-05-29 22:59:08,649 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:08.649219Z"}
2025-05-29 22:59:11,149 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:11.149005Z"}
2025-05-29 22:59:11,619 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 57RJmA1s... sold 0.8812 SOL of PIGA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:11.619425Z"}
2025-05-29 22:59:11,677 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:11.677112Z"}
2025-05-29 22:59:11,999 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6dxzHHxt... sold 0.9901 SOL of GucciGrape", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:11.998907Z"}
2025-05-29 22:59:12,475 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5h3o1HKa... sold 1.9802 SOL of Ringomaru ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:12.475203Z"}
2025-05-29 22:59:15,312 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:15.311961Z"}
2025-05-29 22:59:16,537 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:16.537532Z"}
2025-05-29 22:59:17,763 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6aHopEgb... sold 0.1980 SOL of JREKT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:17.763210Z"}
2025-05-29 22:59:20,381 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 68zCgLXj... sold 0.0753 SOL of hopecoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:20.380913Z"}
2025-05-29 22:59:20,833 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F7ZoaJTd... sold 0.0495 SOL of OXIX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:20.833673Z"}
2025-05-29 22:59:21,260 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: oYspqkGQ... sold 0.0010 SOL of $BC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:21.259920Z"}
2025-05-29 22:59:26,486 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:26.486144Z"}
2025-05-29 22:59:27,457 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3LUxnnHy... sold 0.0099 SOL of Test", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:27.457636Z"}
2025-05-29 22:59:28,688 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HjGscuDo... sold 1.0891 SOL of PEPEJAMAS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:28.688634Z"}
2025-05-29 22:59:28,726 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:28.725980Z"}
2025-05-29 22:59:30,668 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:30.668423Z"}
2025-05-29 22:59:32,815 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:32.815196Z"}
2025-05-29 22:59:32,964 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Cs1hC5WQ... sold 0.9901 SOL of FIP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:32.964501Z"}
2025-05-29 22:59:36,255 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:36.255571Z"}
2025-05-29 22:59:37,122 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: J3ycdr6m... sold 0.0000 SOL of AiBox", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:37.122172Z"}
2025-05-29 22:59:38,384 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:38.384336Z"}
2025-05-29 22:59:45,800 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4si3QznX... sold 0.9000 SOL of QuantumPon", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:45.800124Z"}
2025-05-29 22:59:48,172 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5edqLi3T... sold 0.8911 SOL of PJs", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:48.172158Z"}
2025-05-29 22:59:49,237 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:49.237477Z"}
2025-05-29 22:59:50,641 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:50.641436Z"}
2025-05-29 22:59:51,890 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:51.890853Z"}
2025-05-29 22:59:55,658 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:55.658507Z"}
2025-05-29 22:59:57,083 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:57.083113Z"}
2025-05-29 22:59:58,135 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2gABsvNe... sold 1.2871 SOL of JARGON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T02:59:58.134952Z"}
2025-05-29 23:00:00,120 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2bqRipax... sold 1.0000 SOL of goril", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:00.120065Z"}
2025-05-29 23:00:05,071 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:05.071471Z"}
2025-05-29 23:00:05,684 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CYtc8PXw... sold 3.0000 SOL of fuck pigs", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:05.684759Z"}
2025-05-29 23:00:06,915 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:06.914752Z"}
2025-05-29 23:00:08,327 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:08.327214Z"}
2025-05-29 23:00:10,346 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:10.346660Z"}
2025-05-29 23:00:12,443 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:12.443278Z"}
2025-05-29 23:00:15,283 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:15.283688Z"}
2025-05-29 23:00:18,719 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:18.719719Z"}
2025-05-29 23:00:21,659 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:21.659297Z"}
2025-05-29 23:00:23,587 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:23.587768Z"}
2025-05-29 23:00:25,961 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:25.961180Z"}
2025-05-29 23:00:27,803 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:27.803035Z"}
2025-05-29 23:00:31,539 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CugzjAuy... sold 0.0000 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:31.539313Z"}
2025-05-29 23:00:39,784 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of DUNNIT", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:39.783949Z"}
2025-05-29 23:00:41,321 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of SEXYTEEN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:41.321065Z"}
2025-05-29 23:00:53,507 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G9A2GDdo... sold 1.9802 SOL of GHIJKL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:53.507896Z"}
2025-05-29 23:00:54,302 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DcP2WM4D... sold 0.9901 SOL of Brokecoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:54.302680Z"}
2025-05-29 23:00:59,377 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of MOO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:00:59.377016Z"}
2025-05-29 23:01:01,609 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EdprZfhb... sold 0.9901 SOL of SLAPP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:01.609259Z"}
2025-05-29 23:01:04,591 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FoQZ9ygC... sold 0.4950 SOL of nap", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:04.591806Z"}
2025-05-29 23:01:07,120 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HNEb8Q4G... sold 0.9901 SOL of PTSD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:07.120339Z"}
2025-05-29 23:01:07,182 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of XXXSTREAM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:07.182478Z"}
2025-05-29 23:01:26,728 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of LIVECAM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:26.728191Z"}
2025-05-29 23:01:28,264 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:01:28.264121Z"}
2025-05-29 23:01:28,309 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:01:28.308899Z"}
2025-05-29 23:01:28,454 - PumpFunMonitor - INFO - {"event": "\ud83d\udcca PumpPortal Stats - Tokens: 0, Trades: 120, Tracked: 120", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:28.453754Z"}
2025-05-29 23:01:47,058 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FKkd8sTm... sold 0.1917 SOL of GOLD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:47.058441Z"}
2025-05-29 23:01:47,365 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of FUCKING", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:47.365571Z"}
2025-05-29 23:01:48,412 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: C9uy3GuJ... sold 0.0000 SOL of $Ace", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:48.412195Z"}
2025-05-29 23:01:50,133 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EgVHBs3W... sold 1.9802 SOL of PWOG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:50.133528Z"}
2025-05-29 23:01:52,603 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GD32KhxL... sold 2.4752 SOL of HOE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:01:52.603730Z"}
2025-05-29 23:02:08,142 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of CAMNUDE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:08.142715Z"}
2025-05-29 23:02:09,170 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of ONE1", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:09.169956Z"}
2025-05-29 23:02:14,942 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6J5fDALP... sold 1.4356 SOL of WILF", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:14.942505Z"}
2025-05-29 23:02:15,325 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7fUaeJtT... sold 1.9802 SOL of MILK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:15.325015Z"}
2025-05-29 23:02:15,794 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5x9PwhRg... sold 1.4851 SOL of PIGGEMINI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:15.793276Z"}
2025-05-29 23:02:16,008 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ki9Z1FUd... sold 0.9901 SOL of SPORK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:16.008362Z"}
2025-05-29 23:02:22,679 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5h3o1HKa... sold 1.9802 SOL of FapCoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:22.679287Z"}
2025-05-29 23:02:31,109 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6iTvrLz2... sold 0.0001 SOL of Cokezero", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:31.109338Z"}
2025-05-29 23:02:32,252 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DcP2WM4D... sold 0.9901 SOL of SM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:32.252563Z"}
2025-05-29 23:02:39,180 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8276ex1N... sold 1.0891 SOL of Juiced", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:39.180389Z"}
2025-05-29 23:02:47,510 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: PFJNQDtU... sold 0.1404 SOL of BOB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:47.509905Z"}
2025-05-29 23:02:52,236 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4jkd2jB4... sold 8.9109 SOL of a=(x,y,d", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:52.235822Z"}
2025-05-29 23:02:59,498 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6s2vtjMj... sold 1.4356 SOL of FYAX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:02:59.498170Z"}
2025-05-29 23:03:02,969 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DoNJ3zn5... sold 0.2970 SOL of CZ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:02.969004Z"}
2025-05-29 23:03:08,078 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BJVeQTWH... sold 4.3481 SOL of PALM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:08.078577Z"}
2025-05-29 23:03:32,731 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7F5Q3Mcs... sold 0.9901 SOL of WelBack", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:32.731714Z"}
2025-05-29 23:03:33,707 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 736ps5zW... sold 2.9703 SOL of PiggyCoin", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:33.706911Z"}
2025-05-29 23:03:41,644 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: J3ycdr6m... sold 0.0000 SOL of BLINK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:41.644797Z"}
2025-05-29 23:03:44,933 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8e3GeoMP... sold 0.9901 SOL of BIGWINAI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:44.933805Z"}
2025-05-29 23:03:47,023 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BwaVFCDJ... sold 4.0000 SOL of PIC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:47.023889Z"}
2025-05-29 23:03:48,905 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3kKb4MtN... sold 0.7921 SOL of KIP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:48.905536Z"}
2025-05-29 23:03:48,978 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6NxQtfLn... sold 0.9901 SOL of SPR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:48.978843Z"}
2025-05-29 23:03:51,781 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5JzRjmLS... sold 0.0000 SOL of WAS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:51.780893Z"}
2025-05-29 23:03:58,461 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: GWYgwaKE... sold 0.4950 SOL of Homestar", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:03:58.461402Z"}
2025-05-29 23:04:02,488 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4e3Ykk94... sold 0.9901 SOL of UwU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:04:02.488742Z"}
2025-05-29 23:04:06,563 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7fUaeJtT... sold 1.9802 SOL of VORTEX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:04:06.563406Z"}
2025-05-29 23:04:18,082 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6ruvDYrD... sold 1.9802 SOL of DownPuffer", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:04:18.082248Z"}
2025-05-29 23:04:21,313 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Bchc7TnE... sold 0.3465 SOL of POGE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:04:21.312914Z"}
2025-05-29 23:04:30,992 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EfJcia7p... sold 0.0010 SOL of SpongeQuan", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:04:30.992399Z"}
2025-05-29 23:04:35,095 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2TuxSJD9... sold 1.4356 SOL of Evb", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:04:35.095719Z"}
2025-05-29 23:04:45,795 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 9z6v3Gwj... sold 4.9505 SOL of ok2", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:04:45.795004Z"}
2025-05-29 23:14:51,848 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-30T03:14:51.848616Z"}
2025-05-29 23:14:51,882 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-30T03:14:51.881926Z"}
2025-05-29 23:14:51,890 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:51.890405Z"}
2025-05-29 23:14:52,169 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-30T03:14:52.169387Z"}
2025-05-29 23:14:52,174 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.174264Z"}
2025-05-29 23:14:52,177 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.177410Z"}
2025-05-29 23:14:52,188 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-30T03:14:52.188367Z"}
2025-05-29 23:14:52,226 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.226300Z"}
2025-05-29 23:14:52,247 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.247161Z"}
2025-05-29 23:14:52,262 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.262208Z"}
2025-05-29 23:14:52,272 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.272447Z"}
2025-05-29 23:14:52,298 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.298484Z"}
2025-05-29 23:14:52,312 - SolanaTradingBot - INFO - {"event": "Running 5 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.312187Z"}
2025-05-29 23:14:52,430 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-30T03:14:52.429943Z"}
2025-05-29 23:14:52,435 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-30T03:14:52.435331Z"}
2025-05-29 23:14:52,448 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:14:52.448835Z"}
2025-05-29 23:14:52,453 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.452979Z"}
2025-05-29 23:14:52,473 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.473118Z"}
2025-05-29 23:14:52,497 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.496107Z"}
2025-05-29 23:14:52,515 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:14:52.515241Z"}
2025-05-29 23:14:52,533 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:14:52.533195Z"}
2025-05-29 23:14:52,654 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:14:52.654539Z"}
2025-05-29 23:14:52,675 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:14:52.675673Z"}
2025-05-29 23:14:55,049 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AAz3NBTY... sold 1.4851 SOL of pork", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:14:55.049268Z"}
2025-05-29 23:15:07,306 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7bcjZjCf... sold 2.0000 SOL of FLOPSTERS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:07.306345Z"}
2025-05-29 23:15:09,456 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EFw7H3oM... sold 1.4851 SOL of Tired", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:09.456785Z"}
2025-05-29 23:15:11,343 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4CLKNsFk... sold 2.9703 SOL of bubu", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:11.343890Z"}
2025-05-29 23:15:17,136 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of STRIPPING", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:17.136697Z"}
2025-05-29 23:15:17,356 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dwf1RDeY... sold 0.0990 SOL of boogie", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:17.356787Z"}
2025-05-29 23:15:22,336 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 6BW5GS3e... sold 1.7000 SOL of QuantumPon", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:22.336643Z"}
2025-05-29 23:15:32,494 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of TITSLIVE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:32.494167Z"}
2025-05-29 23:15:43,032 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 2UyNRxTa... sold 1.0891 SOL of Bao-ao", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:43.032453Z"}
2025-05-29 23:15:52,461 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of NAKEDLIVE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:52.461039Z"}
2025-05-29 23:15:55,538 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 736ps5zW... sold 2.9703 SOL of DOVE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:55.538281Z"}
2025-05-29 23:15:58,088 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5upvdXnP... sold 1.0000 SOL of GOOMBA", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:15:58.088812Z"}
2025-05-29 23:16:06,331 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 1kKeYdcS... sold 1.2871 SOL of \u261d\ufe0f\ud83e\udd13", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:06.331205Z"}
2025-05-29 23:16:08,125 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AVAVx7Uz... sold 1.4663 SOL of RIZZMO", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:08.125892Z"}
2025-05-29 23:16:10,722 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AoJaufSS... sold 0.0198 SOL of Snuggles", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:10.722639Z"}
2025-05-29 23:16:16,395 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of LIVESEX", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:16.395141Z"}
2025-05-29 23:16:21,183 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: AjscT69g... sold 0.0495 SOL of COLONIZE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:21.183435Z"}
2025-05-29 23:16:32,747 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: EFw7H3oM... sold 0.9901 SOL of Updog", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:32.746963Z"}
2025-05-29 23:16:34,717 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 736ps5zW... sold 2.9703 SOL of DOVE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:34.717699Z"}
2025-05-29 23:16:39,048 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CZ4MYpjR... sold 0.9901 SOL of BACONBITS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:39.047995Z"}
2025-05-29 23:16:39,255 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4pwXiCb8... sold 0.4400 SOL of mooncat", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:39.255172Z"}
2025-05-29 23:16:42,610 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7Fxyt7xe... sold 0.1500 SOL of BLOWJOB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:42.610827Z"}
2025-05-29 23:16:47,415 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DNS2QCAH... sold 0.0990 SOL of BOB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:47.415607Z"}
2025-05-29 23:16:47,919 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8e3GeoMP... sold 0.8416 SOL of APMSM", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:47.918920Z"}
2025-05-29 23:16:48,104 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BG4Ln2BL... sold 0.0099 SOL of scampig ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:48.104544Z"}
2025-05-29 23:16:49,473 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BPbcYFqD... sold 0.9901 SOL of JARGON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:49.473059Z"}
2025-05-29 23:16:56,052 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Dwf1RDeY... sold 0.0990 SOL of  boogieboy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:56.052166Z"}
2025-05-29 23:16:56,123 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5KQApv8q... sold 3.0000 SOL of BOB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:16:56.122400Z"}
2025-05-29 23:17:05,339 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ErUTKNKi... sold 2.0000 SOL of FENTDEALER", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:05.339236Z"}
2025-05-29 23:17:09,183 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8GfoYoSs... sold 1.3366 SOL of memebook", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:09.183114Z"}
2025-05-29 23:17:18,791 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FWnPg3AF... sold 3.0000 SOL of underpig", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:18.790962Z"}
2025-05-29 23:17:33,876 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 89z8VUY5... sold 2.0000 SOL of TITTIES", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:33.876131Z"}
2025-05-29 23:17:35,988 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7AG5nTc2... sold 1.9802 SOL of UAP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:35.988064Z"}
2025-05-29 23:17:41,603 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 736ps5zW... sold 2.9703 SOL of PLANK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:41.603294Z"}
2025-05-29 23:17:42,314 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7cAU4ZP4... sold 0.0001 SOL of COINCOOKAI", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:42.313993Z"}
2025-05-29 23:17:45,381 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: e1diUwMN... sold 0.6528 SOL of BANKSY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:45.381693Z"}
2025-05-29 23:17:46,919 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BwaVFCDJ... sold 4.0000 SOL of NSTMLINS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:46.919337Z"}
2025-05-29 23:17:46,969 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of SPECIAL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:46.969201Z"}
2025-05-29 23:17:47,968 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HLiE2t8y... sold 1.2079 SOL of DeepSeek", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:47.968346Z"}
2025-05-29 23:17:51,487 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 7fUaeJtT... sold 1.9802 SOL of pih", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:51.487004Z"}
2025-05-29 23:17:57,184 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: Ez2jp3rw... sold 3.0000 SOL of YE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:57.184764Z"}
2025-05-29 23:17:58,294 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 89z8VUY5... sold 2.0000 SOL of DICKLE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:17:58.294036Z"}
2025-05-29 23:18:00,590 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FLKF2omU... sold 57.4654 SOL of WYNNING", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:00.590167Z"}
2025-05-29 23:18:06,033 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 736ps5zW... sold 2.9703 SOL of PLANK", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:06.033188Z"}
2025-05-29 23:18:09,708 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5KQApv8q... sold 3.0000 SOL of DVD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:09.708292Z"}
2025-05-29 23:18:18,074 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ETUygCzy... sold 69.3069 SOL of BABYPITBUL", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:18.074453Z"}
2025-05-29 23:18:25,832 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H3eQvWai... sold 0.3264 SOL of TSN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:25.832524Z"}
2025-05-29 23:18:28,268 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 736ps5zW... sold 2.9703 SOL of DVD", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:28.268506Z"}
2025-05-29 23:18:34,970 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 969z5StN... sold 1.0000 SOL of SLEEPY", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:34.970020Z"}
2025-05-29 23:18:42,106 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 8cztdSB5... sold 1.7822 SOL of ye", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:42.106562Z"}
2025-05-29 23:18:51,252 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: DxRn8Pbp... sold 2.0000 SOL of MCCHICKEN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:51.252131Z"}
2025-05-29 23:18:54,840 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 28FTkZ8N... sold 0.9901 SOL of HTS", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:18:54.840524Z"}
2025-05-29 23:19:03,691 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FwxrB5CP... sold 0.9901 SOL of Pigci", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:03.691627Z"}
2025-05-29 23:19:08,500 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5upvdXnP... sold 2.0000 SOL of GEEKED", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:08.500686Z"}
2025-05-29 23:19:08,916 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: HXNbXHqF... sold 0.9901 SOL of RESUME", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:08.916690Z"}
2025-05-29 23:19:14,700 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: H22GxQU3... sold 0.2376 SOL of ELONPIG", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:14.700252Z"}
2025-05-29 23:19:15,817 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FepaprrH... sold 0.0495 SOL of helly", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:15.817060Z"}
2025-05-29 23:19:19,496 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: CGyNj4k9... sold 0.4950 SOL of 0.000001", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:19.496162Z"}
2025-05-29 23:19:21,241 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FXzFNn2A... sold 0.2077 SOL of OMC", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:21.240962Z"}
2025-05-29 23:19:23,061 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3fEhynYT... sold 1.1881 SOL of fineshyt", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:23.061525Z"}
2025-05-29 23:19:26,273 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FnbXWZD5... sold 1.0000 SOL of TESTICLE", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:26.273391Z"}
2025-05-29 23:19:27,278 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5HAWUyfg... sold 4.0000 SOL of DB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:27.278233Z"}
2025-05-29 23:19:30,478 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BwaVFCDJ... sold 10.0000 SOL of DB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:30.478830Z"}
2025-05-29 23:19:30,549 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 3GfP47xW... sold 5.0000 SOL of DB", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:30.549714Z"}
2025-05-29 23:19:43,784 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: ErUTKNKi... sold 1.0000 SOL of HEISEN ", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:43.784264Z"}
2025-05-29 23:19:48,143 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: C23Sx4E5... sold 0.9901 SOL of pork", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:19:48.143107Z"}
2025-05-29 23:30:50,352 - solana_trading_bot - INFO - {"event": "\ud83d\ude80 Starting Solana Trading Bot (No Telegram)...", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-30T03:30:50.351881Z"}
2025-05-29 23:30:50,362 - solana_trading_bot - INFO - {"event": "============================================================", "logger": "solana_trading_bot", "level": "info", "timestamp": "2025-05-30T03:30:50.359652Z"}
2025-05-29 23:30:50,377 - SolanaTradingBot - INFO - {"event": "Initializing Solana Trading Bot (No Telegram)...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.377856Z"}
2025-05-29 23:30:50,787 - src.data.database - INFO - {"event": "Database initialized successfully", "logger": "src.data.database", "level": "info", "timestamp": "2025-05-30T03:30:50.786901Z"}
2025-05-29 23:30:50,791 - SolanaTradingBot - INFO - {"event": "Database initialized", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.791741Z"}
2025-05-29 23:30:50,798 - SolanaTradingBot - INFO - {"event": "Configuration validation passed", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.795905Z"}
2025-05-29 23:30:50,804 - PumpFunAnalyzer - INFO - {"event": "Pump.fun analyzer initialized", "logger": "PumpFunAnalyzer", "level": "info", "timestamp": "2025-05-30T03:30:50.804801Z"}
2025-05-29 23:30:50,821 - SolanaTradingBot - INFO - {"event": "Bot initialization completed successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.821741Z"}
2025-05-29 23:30:50,840 - SolanaTradingBot - INFO - {"event": "Starting Solana Trading Bot...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.840582Z"}
2025-05-29 23:30:50,844 - SolanaTradingBot - INFO - {"event": "Transaction monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.844321Z"}
2025-05-29 23:30:50,854 - SolanaTradingBot - INFO - {"event": "Pump.fun monitoring started", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.854175Z"}
2025-05-29 23:30:50,887 - SolanaTradingBot - INFO - {"event": "Solana Trading Bot started successfully", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.886418Z"}
2025-05-29 23:30:50,940 - SolanaTradingBot - INFO - {"event": "Running 5 background tasks", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:50.939944Z"}
2025-05-29 23:30:50,992 - SolanaClient - ERROR - {"event": "Failed to initialize Solana client: 'AsyncClient' object has no attribute 'get_health'", "logger": "SolanaClient", "level": "error", "timestamp": "2025-05-30T03:30:50.992371Z"}
2025-05-29 23:30:51,030 - TransactionMonitor - ERROR - {"event": "Failed to start transaction monitoring: 'AsyncClient' object has no attribute 'get_health'", "logger": "TransactionMonitor", "level": "error", "timestamp": "2025-05-30T03:30:51.030544Z"}
2025-05-29 23:30:51,039 - PumpFunMonitor - INFO - {"event": "Starting pump.fun monitoring with PumpPortal WebSocket...", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:30:51.039091Z"}
2025-05-29 23:30:51,055 - SolanaTradingBot - INFO - {"event": "Running periodic wallet analysis...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:51.054865Z"}
2025-05-29 23:30:51,064 - SolanaTradingBot - INFO - {"event": "Risk metrics - Daily P&L: 0.0000 SOL, Active positions: 0, Win rate: 0.00%", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:51.064295Z"}
2025-05-29 23:30:51,079 - SolanaTradingBot - INFO - {"event": "Updating performance metrics...", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:51.078965Z"}
2025-05-29 23:30:51,090 - SolanaTradingBot - INFO - {"event": "Performance summary - Copy trades: 0, Success rate: 0.00%, Daily P&L: 0.0000 SOL", "logger": "SolanaTradingBot", "level": "info", "timestamp": "2025-05-30T03:30:51.090173Z"}
2025-05-29 23:30:51,100 - PumpFunMonitor - INFO - {"event": "Connecting to PumpPortal WebSocket: wss://pumpportal.fun/api/data", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:30:51.100003Z"}
2025-05-29 23:30:51,307 - PumpFunMonitor - INFO - {"event": "\u2705 Connected to PumpPortal WebSocket", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:30:51.306730Z"}
2025-05-29 23:30:51,377 - PumpFunMonitor - INFO - {"event": "\u2705 Subscription successful: Successfully subscribed to token creation events.", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:30:51.377860Z"}
2025-05-29 23:30:56,925 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: F3We3ov8... sold 0.2871 SOL of Legsy", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:30:56.925148Z"}
2025-05-29 23:31:00,503 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: BwaVFCDJ... sold 4.0000 SOL of MOONSUN", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:31:00.503862Z"}
2025-05-29 23:31:02,056 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 4RePs4Ec... sold 4.0000 SOL of MOON", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:31:02.056671Z"}
2025-05-29 23:31:08,528 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: FgArVeo9... sold 0.0297 SOL of RR", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:31:08.528213Z"}
2025-05-29 23:31:17,813 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: 5xvN8nDF... sold 0.0040 SOL of $OUMP", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:31:17.813425Z"}
2025-05-29 23:31:22,675 - PumpFunMonitor - INFO - {"event": "\ud83d\udcb0 Early trade: G9A2GDdo... sold 1.4851 SOL of ZYXWVU", "logger": "PumpFunMonitor", "level": "info", "timestamp": "2025-05-30T03:31:22.675302Z"}
